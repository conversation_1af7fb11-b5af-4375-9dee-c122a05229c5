<svg width="73" height="69" viewBox="0 0 73 69" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_2124_495)">
<rect x="4" width="65" height="65" rx="32.5" fill="#B10303" shape-rendering="crispEdges"/>
<path d="M36.5 22.2144V42.7858" stroke="white" stroke-width="1.92857" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M26.2139 32.5H46.7853" stroke="white" stroke-width="1.92857" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<filter id="filter0_d_2124_495" x="0" y="-4" width="73" height="73" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2124_495"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2124_495" result="shape"/>
</filter>
</defs>
</svg>
