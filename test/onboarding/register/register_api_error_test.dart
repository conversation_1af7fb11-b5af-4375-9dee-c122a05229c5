import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mialamobile/providers/auth_provider.dart';
import 'package:provider/provider.dart';

// Mock AuthProvider that simulates an API error
class MockAuthProvider extends ChangeNotifier implements AuthProvider {
  bool signupCalled = false;
  bool returnSuccess = false;

  @override
  Future<Map<String, dynamic>> signup({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
    required String phone,
    required String accountNumber,
    required String accountName,
    required String bankName,
    required String state,
  }) async {
    signupCalled = true;

    if (returnSuccess) {
      // Simulate a successful API response
      return {
        'success': true,
        'message': 'Registration successful',
      };
    } else {
      // Simulate an API error response
      return {
        'success': false,
        'message': 'Email already exists',
      };
    }
  }

  // Implement other required methods with minimal functionality
  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

void main() {
  testWidgets(
      'Registration page handles API errors correctly without navigation',
      (WidgetTester tester) async {
    // Create a key to access the scaffold messenger for snackbar verification
    final scaffoldMessengerKey = GlobalKey<ScaffoldMessengerState>();
    final mockAuthProvider = MockAuthProvider();
    bool navigated = false;

    // Build a simplified test harness that can show snackbars
    await tester.pumpWidget(
      MaterialApp(
        scaffoldMessengerKey: scaffoldMessengerKey,
        home: ChangeNotifierProvider<AuthProvider>.value(
          value: mockAuthProvider,
          child: Builder(
            builder: (context) {
              return Scaffold(
                body: ElevatedButton(
                  onPressed: () async {
                    // Simulate the error handling code from register.dart
                    final result = await mockAuthProvider.signup(
                      firstName: 'John',
                      lastName: 'Doe',
                      email: '<EMAIL>',
                      password: 'Password123',
                      phone: '**********',
                      accountNumber: '**********',
                      accountName: 'John Doe',
                      bankName: 'Test Bank',
                      state: '********',
                    );

                    if (result['success']) {
                      // This simulates successful registration with navigation
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                              result['message'] ?? 'Registration successful'),
                          backgroundColor: Colors.green,
                          duration: const Duration(seconds: 3),
                        ),
                      );

                      // Simulate navigation
                      navigated = true;
                    } else {
                      // This is the code we're testing from register.dart
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content:
                              Text(result['message'] ?? 'Registration failed'),
                          backgroundColor: Colors.red,
                          duration: const Duration(seconds: 3),
                        ),
                      );
                    }
                  },
                  child: const Text('Register'),
                ),
              );
            },
          ),
        ),
      ),
    );

    // Tap the register button
    await tester.tap(find.text('Register'));
    await tester.pump(); // Start the signup process

    // Verify the signup method was called
    expect(mockAuthProvider.signupCalled, true);

    // Wait for the snackbar to appear
    await tester.pump(const Duration(milliseconds: 500));

    // Verify that a snackbar is shown with the correct error message
    expect(find.byType(SnackBar), findsOneWidget);
    expect(find.text('Email already exists'), findsOneWidget);

    // Verify the snackbar has the correct styling
    final SnackBar snackBar = tester.widget(find.byType(SnackBar));
    expect(snackBar.backgroundColor, Colors.red);
    expect(snackBar.duration, const Duration(seconds: 3));

    // Verify that no navigation occurred on error
    expect(navigated, false,
        reason: 'Should not navigate when there is an error');
  });

  testWidgets('Registration page navigates on successful registration',
      (WidgetTester tester) async {
    // Create a key to access the scaffold messenger for snackbar verification
    final scaffoldMessengerKey = GlobalKey<ScaffoldMessengerState>();
    final mockAuthProvider = MockAuthProvider();
    mockAuthProvider.returnSuccess = true; // Set to return success
    bool navigated = false;

    // Build a simplified test harness that can show snackbars
    await tester.pumpWidget(
      MaterialApp(
        scaffoldMessengerKey: scaffoldMessengerKey,
        home: ChangeNotifierProvider<AuthProvider>.value(
          value: mockAuthProvider,
          child: Builder(
            builder: (context) {
              return Scaffold(
                body: ElevatedButton(
                  onPressed: () async {
                    // Simulate the error handling code from register.dart
                    final result = await mockAuthProvider.signup(
                      firstName: 'John',
                      lastName: 'Doe',
                      email: '<EMAIL>',
                      password: 'Password123',
                      phone: '**********',
                      accountNumber: '**********',
                      accountName: 'John Doe',
                      bankName: 'Test Bank',
                      state: '********',
                    );

                    if (result['success']) {
                      // This simulates successful registration with navigation
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                              result['message'] ?? 'Registration successful'),
                          backgroundColor: Colors.green,
                          duration: const Duration(seconds: 3),
                        ),
                      );

                      // Simulate navigation
                      navigated = true;
                    } else {
                      // This is the code we're testing from register.dart
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content:
                              Text(result['message'] ?? 'Registration failed'),
                          backgroundColor: Colors.red,
                          duration: const Duration(seconds: 3),
                        ),
                      );
                    }
                  },
                  child: const Text('Register'),
                ),
              );
            },
          ),
        ),
      ),
    );

    // Tap the register button
    await tester.tap(find.text('Register'));
    await tester.pump(); // Start the signup process

    // Verify the signup method was called
    expect(mockAuthProvider.signupCalled, true);

    // Wait for the snackbar to appear
    await tester.pump(const Duration(milliseconds: 500));

    // Verify that a snackbar is shown with the success message
    expect(find.byType(SnackBar), findsOneWidget);
    expect(find.text('Registration successful'), findsOneWidget);

    // Verify the snackbar has the correct styling
    final SnackBar snackBar = tester.widget(find.byType(SnackBar));
    expect(snackBar.backgroundColor, Colors.green);
    expect(snackBar.duration, const Duration(seconds: 3));

    // Verify that navigation occurred on success
    expect(navigated, true,
        reason: 'Should navigate when registration is successful');
  });
}
