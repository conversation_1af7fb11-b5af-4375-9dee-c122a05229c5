import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:mialamobile/api/api_service.dart';
import 'package:mialamobile/api/endpoints.dart';
import 'package:mialamobile/components/input_dropdown.dart';
import 'package:mialamobile/models/bank.dart';
import 'package:mialamobile/onboarding/register/register.dart';
import 'package:mialamobile/providers/auth_provider.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

@GenerateMocks([http.Client, ApiService])
import 'bank_dropdown_test.mocks.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  late MockApiService mockApiService;
  late AuthProvider authProvider;
  late MockClient mockClient;

  setUp(() {
    mockApiService = MockApiService();
    mockClient = MockClient();
    authProvider = AuthProvider();
    authProvider.apiService = mockApiService;

    // Set up SharedPreferences mock
    SharedPreferences.setMockInitialValues({});
  });

  group('Bank Dropdown Tests', () {
    testWidgets('Shows loading state while fetching banks',
        (WidgetTester tester) async {
      // Mock the API call to delay response
      when(mockApiService.get(
        ApiEndpoints.bank,
        requiresAuth: false,
        useExtendedTimeout: true,
        retries: 3,
      )).thenAnswer((_) async {
        // Delay to simulate loading
        await Future.delayed(const Duration(milliseconds: 500));
        return ApiResponse<Map<String, dynamic>>(
          success: true,
          data: {
            'responseCode': '00',
            'responseMsg': 'Success',
            'data': [
              {'name': 'Test Bank 1', 'code': '001'},
              {'name': 'Test Bank 2', 'code': '002'},
            ]
          },
        );
      });

      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<AuthProvider>.value(
            value: authProvider,
            child: const RegistrationPage(),
          ),
        ),
      );

      // Initial pump to start the build
      await tester.pump();

      // Verify loading state is shown
      expect(find.text('Loading banks...'), findsOneWidget);

      // Complete the loading
      await tester.pumpAndSettle();

      // Verify banks are loaded
      expect(find.text('Select your bank'), findsOneWidget);
    });

    testWidgets('Shows default "Select your bank" option',
        (WidgetTester tester) async {
      // Mock successful API response
      when(mockApiService.get(
        ApiEndpoints.bank,
        requiresAuth: false,
        useExtendedTimeout: true,
        retries: 3,
      )).thenAnswer((_) async => ApiResponse<Map<String, dynamic>>(
            success: true,
            data: {
              'responseCode': '00',
              'responseMsg': 'Success',
              'data': [
                {'name': 'Test Bank 1', 'code': '001'},
                {'name': 'Test Bank 2', 'code': '002'},
              ]
            },
          ));

      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<AuthProvider>.value(
            value: authProvider,
            child: const RegistrationPage(),
          ),
        ),
      );

      // Wait for the widget to settle
      await tester.pumpAndSettle();

      // Find the dropdown
      final dropdown = find.byType(InputDropdown);
      expect(dropdown, findsOneWidget);

      // Verify the default option is present
      expect(find.text('Select your bank'), findsOneWidget);
    });

    testWidgets('Shows error message when API fails',
        (WidgetTester tester) async {
      // Mock API failure
      when(mockApiService.get(
        ApiEndpoints.bank,
        requiresAuth: false,
        useExtendedTimeout: true,
        retries: 3,
      )).thenAnswer((_) async => ApiResponse<Map<String, dynamic>>(
            success: false,
            error: 'Failed to fetch banks list',
          ));

      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ChangeNotifierProvider<AuthProvider>.value(
              value: authProvider,
              child: const RegistrationPage(),
            ),
          ),
        ),
      );

      // Wait for the widget to settle
      await tester.pumpAndSettle();

      // Verify error message is shown
      expect(find.text('Failed to fetch banks list'), findsOneWidget);
    });

    testWidgets('Uses cached banks when API times out',
        (WidgetTester tester) async {
      // Set up cached banks in SharedPreferences
      SharedPreferences.setMockInitialValues({
        'cached_banks': jsonEncode([
          {'name': 'Cached Bank 1', 'code': 'C001'},
          {'name': 'Cached Bank 2', 'code': 'C002'},
        ]),
      });

      // Mock API timeout
      when(mockApiService.get(
        ApiEndpoints.bank,
        requiresAuth: false,
        useExtendedTimeout: true,
        retries: 3,
      )).thenAnswer((_) async {
        throw TimeoutException('The server is taking too long to respond');
      });

      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ChangeNotifierProvider<AuthProvider>.value(
              value: authProvider,
              child: const RegistrationPage(),
            ),
          ),
        ),
      );

      // Wait for the widget to settle
      await tester.pumpAndSettle();

      // Verify cached banks are used
      expect(find.text('Using cached banks list'), findsOneWidget);
    });
  });
}
