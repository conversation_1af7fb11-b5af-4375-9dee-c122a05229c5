import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mialamobile/providers/auth_provider.dart';

// Simple widget that simulates showing an error from API in a snackbar
class ErrorDisplayWidget extends StatelessWidget {
  final String errorMessage;

  const ErrorDisplayWidget({super.key, required this.errorMessage});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            // This simulates what happens in the register.dart file when an API error occurs
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(errorMessage),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 3),
              ),
            );
          },
          child: const Text('Show API Error'),
        ),
      ),
    );
  }
}

// Mock AuthProvider that simulates an API error
class MockAuthProvider extends ChangeNotifier implements AuthProvider {
  @override
  Future<Map<String, dynamic>> signup({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
    required String phone,
    required String accountNumber,
    required String accountName,
    required String bankName,
    required String state,
  }) async {
    // Simulate an API error response
    return {
      'success': false,
      'message': 'Email already exists',
    };
  }

  // Implement other required methods with minimal functionality
  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

void main() {
  testWidgets('Shows API error in snackbar', (WidgetTester tester) async {
    const errorMessage = 'Email already exists';

    // Build our test widget
    await tester.pumpWidget(
      const MaterialApp(
        home: ErrorDisplayWidget(errorMessage: errorMessage),
      ),
    );

    // Tap the button to show the error
    await tester.tap(find.text('Show API Error'));

    // Rebuild the widget after the tap and wait for animations
    await tester.pump();
    await tester.pump(const Duration(milliseconds: 500));

    // Verify that a snackbar is shown with the correct error message
    expect(find.byType(SnackBar), findsOneWidget);
    expect(find.text(errorMessage), findsOneWidget);
  });

  testWidgets('Snackbar has correct styling for errors',
      (WidgetTester tester) async {
    const errorMessage = 'Email already exists';

    // Build our test widget
    await tester.pumpWidget(
      const MaterialApp(
        home: ErrorDisplayWidget(errorMessage: errorMessage),
      ),
    );

    // Tap the button to show the error
    await tester.tap(find.text('Show API Error'));

    // Rebuild the widget after the tap
    await tester.pump();
    await tester.pump(const Duration(milliseconds: 500));

    // Find the snackbar
    final snackBarFinder = find.byType(SnackBar);
    expect(snackBarFinder, findsOneWidget);

    // Verify the snackbar has red background color
    final SnackBar snackBar = tester.widget(snackBarFinder);
    expect(snackBar.backgroundColor, Colors.red);

    // Verify the duration is 3 seconds
    expect(snackBar.duration, const Duration(seconds: 3));
  });
}
