import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  testWidgets('Shows validation error in snackbar',
      (WidgetTester tester) async {
    const validationError = 'Email is required';

    // Build a test widget that simulates showing a validation error
    await tester.pumpWidget(
      MaterialApp(
        home: Builder(
          builder: (context) {
            return Scaffold(
              body: Center(
                child: ElevatedButton(
                  onPressed: () {
                    // This simulates what happens in register.dart for validation errors
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(validationError),
                        backgroundColor: Colors.red,
                        duration: Duration(seconds: 3),
                      ),
                    );
                  },
                  child: const Text('Validate Form'),
                ),
              ),
            );
          },
        ),
      ),
    );

    // Tap the button to trigger validation
    await tester.tap(find.text('Validate Form'));

    // Rebuild the widget after the tap
    await tester.pump();
    await tester.pump(const Duration(milliseconds: 500));

    // Verify that a snackbar is shown with the validation error
    expect(find.byType(SnackBar), findsOneWidget);
    expect(find.text(validationError), findsOneWidget);

    // Verify the snackbar has the correct styling
    final SnackBar snackBar = tester.widget(find.byType(SnackBar));
    expect(snackBar.backgroundColor, Colors.red);
    expect(snackBar.duration, const Duration(seconds: 3));
  });

  testWidgets('Form validation code correctly shows first error',
      (WidgetTester tester) async {
    // Build a test widget that simulates the form validation logic
    await tester.pumpWidget(
      MaterialApp(
        home: Builder(
          builder: (context) {
            return Scaffold(
              body: Center(
                child: ElevatedButton(
                  onPressed: () {
                    // This simulates the form validation logic in register.dart
                    final Map<String, String> fieldErrors = {
                      'firstName': '',
                      'lastName': '',
                      'email': 'Email is required',
                      'phone': '',
                      'password': 'Password must be at least 8 characters',
                    };

                    // Find the first error to display
                    final firstError = fieldErrors.entries
                        .firstWhere((entry) => entry.value.isNotEmpty,
                            orElse: () => const MapEntry(
                                '', 'Please fix the errors in the form'))
                        .value;

                    // Show the error in a snackbar
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(firstError),
                        backgroundColor: Colors.red,
                        duration: const Duration(seconds: 3),
                      ),
                    );
                  },
                  child: const Text('Validate'),
                ),
              ),
            );
          },
        ),
      ),
    );

    // Tap the button to trigger validation
    await tester.tap(find.text('Validate'));

    // Rebuild the widget after the tap
    await tester.pump();
    await tester.pump(const Duration(milliseconds: 500));

    // Verify that a snackbar is shown with the first validation error
    expect(find.byType(SnackBar), findsOneWidget);
    expect(find.text('Email is required'),
        findsOneWidget); // Should show the first error
  });
}
