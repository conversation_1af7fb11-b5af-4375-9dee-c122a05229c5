// Mocks generated by Mockito 5.4.6 from annotations
// in mialamobile/test/onboarding/register/register_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i8;
import 'dart:ui' as _i10;

import 'package:mialamobile/api/api_service.dart' as _i7;
import 'package:mialamobile/api/models/user_model.dart' as _i9;
import 'package:mialamobile/models/bank.dart' as _i3;
import 'package:mialamobile/models/delivery.dart' as _i5;
import 'package:mialamobile/models/notification.dart' as _i6;
import 'package:mialamobile/models/state.dart' as _i4;
import 'package:mialamobile/providers/auth_provider.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [AuthProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthProvider extends _i1.Mock implements _i2.AuthProvider {
  MockAuthProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isLoading => (super.noSuchMethod(
        Invocation.getter(#isLoading),
        returnValue: false,
      ) as bool);

  @override
  bool get isAuthenticated => (super.noSuchMethod(
        Invocation.getter(#isAuthenticated),
        returnValue: false,
      ) as bool);

  @override
  List<_i3.Bank> get banks => (super.noSuchMethod(
        Invocation.getter(#banks),
        returnValue: <_i3.Bank>[],
      ) as List<_i3.Bank>);

  @override
  bool get isLoadingBanks => (super.noSuchMethod(
        Invocation.getter(#isLoadingBanks),
        returnValue: false,
      ) as bool);

  @override
  List<_i4.State> get states => (super.noSuchMethod(
        Invocation.getter(#states),
        returnValue: <_i4.State>[],
      ) as List<_i4.State>);

  @override
  bool get isLoadingStates => (super.noSuchMethod(
        Invocation.getter(#isLoadingStates),
        returnValue: false,
      ) as bool);

  @override
  bool get isLoadingDva => (super.noSuchMethod(
        Invocation.getter(#isLoadingDva),
        returnValue: false,
      ) as bool);

  @override
  bool get isLoadingDeliverySummary => (super.noSuchMethod(
        Invocation.getter(#isLoadingDeliverySummary),
        returnValue: false,
      ) as bool);

  @override
  List<_i5.Delivery> get deliveries => (super.noSuchMethod(
        Invocation.getter(#deliveries),
        returnValue: <_i5.Delivery>[],
      ) as List<_i5.Delivery>);

  @override
  bool get isLoadingDeliveries => (super.noSuchMethod(
        Invocation.getter(#isLoadingDeliveries),
        returnValue: false,
      ) as bool);

  @override
  List<_i6.NotificationModel> get notifications => (super.noSuchMethod(
        Invocation.getter(#notifications),
        returnValue: <_i6.NotificationModel>[],
      ) as List<_i6.NotificationModel>);

  @override
  bool get isLoadingNotifications => (super.noSuchMethod(
        Invocation.getter(#isLoadingNotifications),
        returnValue: false,
      ) as bool);

  @override
  int get unreadNotificationsCount => (super.noSuchMethod(
        Invocation.getter(#unreadNotificationsCount),
        returnValue: 0,
      ) as int);

  @override
  Map<String, List<_i6.NotificationModel>> get groupedNotifications =>
      (super.noSuchMethod(
        Invocation.getter(#groupedNotifications),
        returnValue: <String, List<_i6.NotificationModel>>{},
      ) as Map<String, List<_i6.NotificationModel>>);

  @override
  set apiService(_i7.ApiService? service) => super.noSuchMethod(
        Invocation.setter(
          #apiService,
          service,
        ),
        returnValueForMissingStub: null,
      );

  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
      ) as bool);

  @override
  _i8.Future<bool> checkAuthentication() => (super.noSuchMethod(
        Invocation.method(
          #checkAuthentication,
          [],
        ),
        returnValue: _i8.Future<bool>.value(false),
      ) as _i8.Future<bool>);

  @override
  _i8.Future<bool> login(
    String? email,
    String? password,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #login,
          [
            email,
            password,
          ],
        ),
        returnValue: _i8.Future<bool>.value(false),
      ) as _i8.Future<bool>);

  @override
  _i8.Future<Map<String, dynamic>> logout() => (super.noSuchMethod(
        Invocation.method(
          #logout,
          [],
        ),
        returnValue:
            _i8.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i8.Future<Map<String, dynamic>>);

  @override
  _i8.Future<void> clearAuthenticationForPendingApproval() =>
      (super.noSuchMethod(
        Invocation.method(
          #clearAuthenticationForPendingApproval,
          [],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<Map<String, dynamic>> verifyBankAccount({
    required String? accountNumber,
    required String? bankCode,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #verifyBankAccount,
          [],
          {
            #accountNumber: accountNumber,
            #bankCode: bankCode,
          },
        ),
        returnValue:
            _i8.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i8.Future<Map<String, dynamic>>);

  @override
  _i8.Future<Map<String, dynamic>> fetchBanks() => (super.noSuchMethod(
        Invocation.method(
          #fetchBanks,
          [],
        ),
        returnValue:
            _i8.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i8.Future<Map<String, dynamic>>);

  @override
  _i8.Future<Map<String, dynamic>> fetchStates() => (super.noSuchMethod(
        Invocation.method(
          #fetchStates,
          [],
        ),
        returnValue:
            _i8.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i8.Future<Map<String, dynamic>>);

  @override
  _i8.Future<Map<String, dynamic>> forgotPassword({required String? email}) =>
      (super.noSuchMethod(
        Invocation.method(
          #forgotPassword,
          [],
          {#email: email},
        ),
        returnValue:
            _i8.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i8.Future<Map<String, dynamic>>);

  @override
  _i8.Future<Map<String, dynamic>> verifyOtpForForgotPassword({
    required String? email,
    required String? otp,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #verifyOtpForForgotPassword,
          [],
          {
            #email: email,
            #otp: otp,
          },
        ),
        returnValue:
            _i8.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i8.Future<Map<String, dynamic>>);

  @override
  _i8.Future<Map<String, dynamic>> resetPassword({
    required String? token,
    required String? newPassword,
    String? email,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #resetPassword,
          [],
          {
            #token: token,
            #newPassword: newPassword,
            #email: email,
          },
        ),
        returnValue:
            _i8.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i8.Future<Map<String, dynamic>>);

  @override
  _i8.Future<Map<String, dynamic>> signup({
    required String? firstName,
    required String? lastName,
    required String? email,
    required String? password,
    required String? phone,
    required String? accountNumber,
    required String? accountName,
    required String? bankName,
    required String? state,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #signup,
          [],
          {
            #firstName: firstName,
            #lastName: lastName,
            #email: email,
            #password: password,
            #phone: phone,
            #accountNumber: accountNumber,
            #accountName: accountName,
            #bankName: bankName,
            #state: state,
          },
        ),
        returnValue:
            _i8.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i8.Future<Map<String, dynamic>>);

  @override
  _i8.Future<bool> fetchDvaInfo() => (super.noSuchMethod(
        Invocation.method(
          #fetchDvaInfo,
          [],
        ),
        returnValue: _i8.Future<bool>.value(false),
      ) as _i8.Future<bool>);

  @override
  _i8.Future<bool> fetchDeliverySummary() => (super.noSuchMethod(
        Invocation.method(
          #fetchDeliverySummary,
          [],
        ),
        returnValue: _i8.Future<bool>.value(false),
      ) as _i8.Future<bool>);

  @override
  _i8.Future<bool> fetchDeliveries() => (super.noSuchMethod(
        Invocation.method(
          #fetchDeliveries,
          [],
        ),
        returnValue: _i8.Future<bool>.value(false),
      ) as _i8.Future<bool>);

  @override
  _i8.Future<bool> fetchNotifications() => (super.noSuchMethod(
        Invocation.method(
          #fetchNotifications,
          [],
        ),
        returnValue: _i8.Future<bool>.value(false),
      ) as _i8.Future<bool>);

  @override
  _i8.Future<bool> markNotificationAsRead(int? notificationId) =>
      (super.noSuchMethod(
        Invocation.method(
          #markNotificationAsRead,
          [notificationId],
        ),
        returnValue: _i8.Future<bool>.value(false),
      ) as _i8.Future<bool>);

  @override
  _i8.Future<bool> markAllNotificationsAsRead() => (super.noSuchMethod(
        Invocation.method(
          #markAllNotificationsAsRead,
          [],
        ),
        returnValue: _i8.Future<bool>.value(false),
      ) as _i8.Future<bool>);

  @override
  List<_i6.NotificationModel> filterNotifications(String? query) =>
      (super.noSuchMethod(
        Invocation.method(
          #filterNotifications,
          [query],
        ),
        returnValue: <_i6.NotificationModel>[],
      ) as List<_i6.NotificationModel>);

  @override
  void setUserForTesting(_i9.User? user) => super.noSuchMethod(
        Invocation.method(
          #setUserForTesting,
          [user],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(_i10.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(_i10.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
