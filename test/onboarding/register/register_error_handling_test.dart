import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  testWidgets('Registration page shows snackbar for API errors',
      (WidgetTester tester) async {
    // Create a simple test widget that can show snackbars
    final scaffoldKey = GlobalKey<ScaffoldState>();
    final messengerKey = GlobalKey<ScaffoldMessengerState>();

    await tester.pumpWidget(
      MaterialApp(
        scaffoldMessengerKey: messengerKey,
        home: Scaffold(
          key: scaffoldKey,
          body: Builder(
            builder: (context) {
              return ElevatedButton(
                onPressed: () {
                  // This simulates what our code does when an API error occurs
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Email already exists'),
                      backgroundColor: Colors.red,
                      duration: Duration(seconds: 3),
                    ),
                  );
                },
                child: const Text('Show Error'),
              );
            },
          ),
        ),
      ),
    );

    // Tap the button to show the snackbar
    await tester.tap(find.text('Show Error'));
    await tester.pump(); // Start the snackbar animation
    await tester
        .pump(const Duration(milliseconds: 750)); // Animation in progress

    // Verify the snackbar is shown with the correct error message
    expect(find.byType(SnackBar), findsOneWidget);
    expect(find.text('Email already exists'), findsOneWidget);
  });

  testWidgets('Registration page shows snackbar for validation errors',
      (WidgetTester tester) async {
    // Create a simple test widget that can show snackbars
    final scaffoldKey = GlobalKey<ScaffoldState>();
    final messengerKey = GlobalKey<ScaffoldMessengerState>();

    await tester.pumpWidget(
      MaterialApp(
        scaffoldMessengerKey: messengerKey,
        home: Scaffold(
          key: scaffoldKey,
          body: Builder(
            builder: (context) {
              return ElevatedButton(
                onPressed: () {
                  // This simulates what our code does when a validation error occurs
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Email is required'),
                      backgroundColor: Colors.red,
                      duration: Duration(seconds: 3),
                    ),
                  );
                },
                child: const Text('Show Validation Error'),
              );
            },
          ),
        ),
      ),
    );

    // Tap the button to show the snackbar
    await tester.tap(find.text('Show Validation Error'));
    await tester.pump(); // Start the snackbar animation
    await tester
        .pump(const Duration(milliseconds: 750)); // Animation in progress

    // Verify the snackbar is shown with the correct validation error message
    expect(find.byType(SnackBar), findsOneWidget);
    expect(find.text('Email is required'), findsOneWidget);
  });
}
