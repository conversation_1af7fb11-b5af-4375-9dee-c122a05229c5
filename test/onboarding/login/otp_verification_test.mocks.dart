// Mocks generated by Mockito 5.4.6 from annotations
// in mialamobile/test/onboarding/login/otp_verification_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;
import 'dart:ui' as _i11;

import 'package:http/http.dart' as _i3;
import 'package:mialamobile/api/api_service.dart' as _i2;
import 'package:mialamobile/api/models/user_model.dart' as _i10;
import 'package:mialamobile/models/bank.dart' as _i6;
import 'package:mialamobile/models/delivery.dart' as _i8;
import 'package:mialamobile/models/notification.dart' as _i9;
import 'package:mialamobile/models/state.dart' as _i7;
import 'package:mialamobile/providers/auth_provider.dart' as _i5;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeApiResponse_0<T1> extends _i1.SmartFake
    implements _i2.ApiResponse<T1> {
  _FakeApiResponse_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [ApiService].
///
/// See the documentation for Mockito's code generation for more information.
class MockApiService extends _i1.Mock implements _i2.ApiService {
  MockApiService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  set httpClient(_i3.Client? client) => super.noSuchMethod(
        Invocation.setter(
          #httpClient,
          client,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set hasInternetConnectionOverride(bool? value) => super.noSuchMethod(
        Invocation.setter(
          #hasInternetConnectionOverride,
          value,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i4.Future<String?> getToken() => (super.noSuchMethod(
        Invocation.method(
          #getToken,
          [],
        ),
        returnValue: _i4.Future<String?>.value(),
      ) as _i4.Future<String?>);

  @override
  _i4.Future<void> saveToken(
    String? token, {
    int? expiresInDays = 30,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveToken,
          [token],
          {#expiresInDays: expiresInDays},
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<bool> isTokenExpired() => (super.noSuchMethod(
        Invocation.method(
          #isTokenExpired,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<void> clearToken() => (super.noSuchMethod(
        Invocation.method(
          #clearToken,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> saveUserData(Map<String, dynamic>? userData) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveUserData,
          [userData],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<Map<String, dynamic>?> getUserData() => (super.noSuchMethod(
        Invocation.method(
          #getUserData,
          [],
        ),
        returnValue: _i4.Future<Map<String, dynamic>?>.value(),
      ) as _i4.Future<Map<String, dynamic>?>);

  @override
  _i4.Future<void> clearUserData() => (super.noSuchMethod(
        Invocation.method(
          #clearUserData,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<bool> hasInternetConnection() => (super.noSuchMethod(
        Invocation.method(
          #hasInternetConnection,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<_i2.ApiResponse<T>> post<T>(
    String? url, {
    Map<String, dynamic>? body,
    bool? requiresAuth = true,
    int? retries = 2,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #post,
          [url],
          {
            #body: body,
            #requiresAuth: requiresAuth,
            #retries: retries,
          },
        ),
        returnValue: _i4.Future<_i2.ApiResponse<T>>.value(_FakeApiResponse_0<T>(
          this,
          Invocation.method(
            #post,
            [url],
            {
              #body: body,
              #requiresAuth: requiresAuth,
              #retries: retries,
            },
          ),
        )),
      ) as _i4.Future<_i2.ApiResponse<T>>);

  @override
  _i4.Future<_i2.ApiResponse<T>> get<T>(
    String? url, {
    Map<String, dynamic>? queryParams,
    bool? requiresAuth = true,
    int? retries = 2,
    bool? useExtendedTimeout = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #get,
          [url],
          {
            #queryParams: queryParams,
            #requiresAuth: requiresAuth,
            #retries: retries,
            #useExtendedTimeout: useExtendedTimeout,
          },
        ),
        returnValue: _i4.Future<_i2.ApiResponse<T>>.value(_FakeApiResponse_0<T>(
          this,
          Invocation.method(
            #get,
            [url],
            {
              #queryParams: queryParams,
              #requiresAuth: requiresAuth,
              #retries: retries,
              #useExtendedTimeout: useExtendedTimeout,
            },
          ),
        )),
      ) as _i4.Future<_i2.ApiResponse<T>>);

  @override
  _i4.Future<Map<String, dynamic>> testBanksListApi() => (super.noSuchMethod(
        Invocation.method(
          #testBanksListApi,
          [],
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);
}

/// A class which mocks [AuthProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthProvider extends _i1.Mock implements _i5.AuthProvider {
  MockAuthProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isLoading => (super.noSuchMethod(
        Invocation.getter(#isLoading),
        returnValue: false,
      ) as bool);

  @override
  bool get isAuthenticated => (super.noSuchMethod(
        Invocation.getter(#isAuthenticated),
        returnValue: false,
      ) as bool);

  @override
  List<_i6.Bank> get banks => (super.noSuchMethod(
        Invocation.getter(#banks),
        returnValue: <_i6.Bank>[],
      ) as List<_i6.Bank>);

  @override
  bool get isLoadingBanks => (super.noSuchMethod(
        Invocation.getter(#isLoadingBanks),
        returnValue: false,
      ) as bool);

  @override
  List<_i7.State> get states => (super.noSuchMethod(
        Invocation.getter(#states),
        returnValue: <_i7.State>[],
      ) as List<_i7.State>);

  @override
  bool get isLoadingStates => (super.noSuchMethod(
        Invocation.getter(#isLoadingStates),
        returnValue: false,
      ) as bool);

  @override
  bool get isLoadingDva => (super.noSuchMethod(
        Invocation.getter(#isLoadingDva),
        returnValue: false,
      ) as bool);

  @override
  bool get isLoadingDeliverySummary => (super.noSuchMethod(
        Invocation.getter(#isLoadingDeliverySummary),
        returnValue: false,
      ) as bool);

  @override
  List<_i8.Delivery> get deliveries => (super.noSuchMethod(
        Invocation.getter(#deliveries),
        returnValue: <_i8.Delivery>[],
      ) as List<_i8.Delivery>);

  @override
  bool get isLoadingDeliveries => (super.noSuchMethod(
        Invocation.getter(#isLoadingDeliveries),
        returnValue: false,
      ) as bool);

  @override
  List<_i9.NotificationModel> get notifications => (super.noSuchMethod(
        Invocation.getter(#notifications),
        returnValue: <_i9.NotificationModel>[],
      ) as List<_i9.NotificationModel>);

  @override
  bool get isLoadingNotifications => (super.noSuchMethod(
        Invocation.getter(#isLoadingNotifications),
        returnValue: false,
      ) as bool);

  @override
  int get unreadNotificationsCount => (super.noSuchMethod(
        Invocation.getter(#unreadNotificationsCount),
        returnValue: 0,
      ) as int);

  @override
  Map<String, List<_i9.NotificationModel>> get groupedNotifications =>
      (super.noSuchMethod(
        Invocation.getter(#groupedNotifications),
        returnValue: <String, List<_i9.NotificationModel>>{},
      ) as Map<String, List<_i9.NotificationModel>>);

  @override
  set apiService(_i2.ApiService? service) => super.noSuchMethod(
        Invocation.setter(
          #apiService,
          service,
        ),
        returnValueForMissingStub: null,
      );

  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
      ) as bool);

  @override
  _i4.Future<bool> checkAuthentication() => (super.noSuchMethod(
        Invocation.method(
          #checkAuthentication,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> login(
    String? email,
    String? password,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #login,
          [
            email,
            password,
          ],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<Map<String, dynamic>> logout() => (super.noSuchMethod(
        Invocation.method(
          #logout,
          [],
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<void> clearAuthenticationForPendingApproval() =>
      (super.noSuchMethod(
        Invocation.method(
          #clearAuthenticationForPendingApproval,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<Map<String, dynamic>> verifyBankAccount({
    required String? accountNumber,
    required String? bankCode,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #verifyBankAccount,
          [],
          {
            #accountNumber: accountNumber,
            #bankCode: bankCode,
          },
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<Map<String, dynamic>> fetchBanks() => (super.noSuchMethod(
        Invocation.method(
          #fetchBanks,
          [],
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<Map<String, dynamic>> fetchStates() => (super.noSuchMethod(
        Invocation.method(
          #fetchStates,
          [],
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<Map<String, dynamic>> forgotPassword({required String? email}) =>
      (super.noSuchMethod(
        Invocation.method(
          #forgotPassword,
          [],
          {#email: email},
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<Map<String, dynamic>> verifyOtpForForgotPassword({
    required String? email,
    required String? otp,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #verifyOtpForForgotPassword,
          [],
          {
            #email: email,
            #otp: otp,
          },
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<Map<String, dynamic>> resetPassword({
    required String? token,
    required String? newPassword,
    String? email,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #resetPassword,
          [],
          {
            #token: token,
            #newPassword: newPassword,
            #email: email,
          },
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<Map<String, dynamic>> signup({
    required String? firstName,
    required String? lastName,
    required String? email,
    required String? password,
    required String? phone,
    required String? accountNumber,
    required String? accountName,
    required String? bankName,
    required String? state,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #signup,
          [],
          {
            #firstName: firstName,
            #lastName: lastName,
            #email: email,
            #password: password,
            #phone: phone,
            #accountNumber: accountNumber,
            #accountName: accountName,
            #bankName: bankName,
            #state: state,
          },
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<bool> fetchDvaInfo() => (super.noSuchMethod(
        Invocation.method(
          #fetchDvaInfo,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> fetchDeliverySummary() => (super.noSuchMethod(
        Invocation.method(
          #fetchDeliverySummary,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> fetchDeliveries() => (super.noSuchMethod(
        Invocation.method(
          #fetchDeliveries,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> fetchNotifications() => (super.noSuchMethod(
        Invocation.method(
          #fetchNotifications,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> markNotificationAsRead(int? notificationId) =>
      (super.noSuchMethod(
        Invocation.method(
          #markNotificationAsRead,
          [notificationId],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> markAllNotificationsAsRead() => (super.noSuchMethod(
        Invocation.method(
          #markAllNotificationsAsRead,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  List<_i9.NotificationModel> filterNotifications(String? query) =>
      (super.noSuchMethod(
        Invocation.method(
          #filterNotifications,
          [query],
        ),
        returnValue: <_i9.NotificationModel>[],
      ) as List<_i9.NotificationModel>);

  @override
  void setUserForTesting(_i10.User? user) => super.noSuchMethod(
        Invocation.method(
          #setUserForTesting,
          [user],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(_i11.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(_i11.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
