import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mialamobile/api/api_service.dart';
import 'package:mialamobile/onboarding/login/otp_verification.dart';
import 'package:mialamobile/onboarding/login/verification_type.dart';
import 'package:mialamobile/providers/auth_provider.dart';
import 'package:mockito/annotations.dart';
import 'package:provider/provider.dart';

import 'otp_verification_test.mocks.dart';

@GenerateMocks([ApiService, AuthProvider])
void main() {
  late MockApiService mockApiService;
  late MockAuthProvider mockAuthProvider;

  setUp(() {
    mockApiService = MockApiService();
    mockAuthProvider = MockAuthProvider();
  });

  testWidgets('OTP verification screen renders correctly for account creation',
      (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(
      MaterialApp(
        home: ChangeNotifierProvider<AuthProvider>.value(
          value: mockAuthProvider,
          child: const OtpVerification(
            email: '<EMAIL>',
            verificationType: VerificationType.accountCreation,
          ),
        ),
      ),
    );

    // Verify that the OTP verification screen is displayed
    expect(find.text('OTP Verification'), findsOneWidget);
    expect(find.text('We sent a code to verify your account'), findsOneWidget);
    expect(find.text('Didn\'t receive code?'), findsOneWidget);

    // Verify that the resend timer is displayed
    expect(find.textContaining('Resend'), findsOneWidget);

    // Verify that the Done button is displayed
    expect(find.text('Done'), findsOneWidget);
  });

  testWidgets('OTP verification screen renders correctly for forgot password',
      (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(
      MaterialApp(
        home: ChangeNotifierProvider<AuthProvider>.value(
          value: mockAuthProvider,
          child: const OtpVerification(
            email: '<EMAIL>',
            verificationType: VerificationType.forgotPassword,
          ),
        ),
      ),
    );

    // Verify that the OTP verification screen is displayed
    expect(find.text('OTP Verification'), findsOneWidget);
    expect(find.text('We sent a code to reset your password'), findsOneWidget);
    expect(find.text('Didn\'t receive code?'), findsOneWidget);

    // Verify that the resend timer is displayed
    expect(find.textContaining('Resend'), findsOneWidget);

    // Verify that the Done button is displayed
    expect(find.text('Done'), findsOneWidget);
  });

  // Additional tests would be added here for:
  // - Testing OTP input validation
  // - Testing API call success and failure scenarios
  // - Testing resend functionality
  // - Testing timer countdown
}
