// Mocks generated by Mockito 5.4.6 from annotations
// in mialamobile/test/providers/auth_provider_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:http/http.dart' as _i3;
import 'package:mialamobile/api/api_service.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeApiResponse_0<T1> extends _i1.SmartFake
    implements _i2.ApiResponse<T1> {
  _FakeApiResponse_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [ApiService].
///
/// See the documentation for Mockito's code generation for more information.
class MockApiService extends _i1.Mock implements _i2.ApiService {
  MockApiService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  set httpClient(_i3.Client? client) => super.noSuchMethod(
        Invocation.setter(
          #httpClient,
          client,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set hasInternetConnectionOverride(bool? value) => super.noSuchMethod(
        Invocation.setter(
          #hasInternetConnectionOverride,
          value,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i4.Future<String?> getToken() => (super.noSuchMethod(
        Invocation.method(
          #getToken,
          [],
        ),
        returnValue: _i4.Future<String?>.value(),
      ) as _i4.Future<String?>);

  @override
  _i4.Future<void> saveToken(
    String? token, {
    int? expiresInDays = 30,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveToken,
          [token],
          {#expiresInDays: expiresInDays},
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<bool> isTokenExpired() => (super.noSuchMethod(
        Invocation.method(
          #isTokenExpired,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<void> clearToken() => (super.noSuchMethod(
        Invocation.method(
          #clearToken,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> saveUserData(Map<String, dynamic>? userData) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveUserData,
          [userData],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<Map<String, dynamic>?> getUserData() => (super.noSuchMethod(
        Invocation.method(
          #getUserData,
          [],
        ),
        returnValue: _i4.Future<Map<String, dynamic>?>.value(),
      ) as _i4.Future<Map<String, dynamic>?>);

  @override
  _i4.Future<void> clearUserData() => (super.noSuchMethod(
        Invocation.method(
          #clearUserData,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<bool> hasInternetConnection() => (super.noSuchMethod(
        Invocation.method(
          #hasInternetConnection,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<_i2.ApiResponse<T>> post<T>(
    String? url, {
    Map<String, dynamic>? body,
    bool? requiresAuth = true,
    int? retries = 2,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #post,
          [url],
          {
            #body: body,
            #requiresAuth: requiresAuth,
            #retries: retries,
          },
        ),
        returnValue: _i4.Future<_i2.ApiResponse<T>>.value(_FakeApiResponse_0<T>(
          this,
          Invocation.method(
            #post,
            [url],
            {
              #body: body,
              #requiresAuth: requiresAuth,
              #retries: retries,
            },
          ),
        )),
      ) as _i4.Future<_i2.ApiResponse<T>>);

  @override
  _i4.Future<_i2.ApiResponse<T>> get<T>(
    String? url, {
    Map<String, dynamic>? queryParams,
    bool? requiresAuth = true,
    int? retries = 2,
    bool? useExtendedTimeout = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #get,
          [url],
          {
            #queryParams: queryParams,
            #requiresAuth: requiresAuth,
            #retries: retries,
            #useExtendedTimeout: useExtendedTimeout,
          },
        ),
        returnValue: _i4.Future<_i2.ApiResponse<T>>.value(_FakeApiResponse_0<T>(
          this,
          Invocation.method(
            #get,
            [url],
            {
              #queryParams: queryParams,
              #requiresAuth: requiresAuth,
              #retries: retries,
              #useExtendedTimeout: useExtendedTimeout,
            },
          ),
        )),
      ) as _i4.Future<_i2.ApiResponse<T>>);

  @override
  _i4.Future<Map<String, dynamic>> testBanksListApi() => (super.noSuchMethod(
        Invocation.method(
          #testBanksListApi,
          [],
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);
}
