import 'package:flutter_test/flutter_test.dart';
import 'package:mialamobile/api/api_service.dart';
import 'package:mialamobile/api/endpoints.dart';
import 'package:mialamobile/providers/auth_provider.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

@GenerateMocks([ApiService])
import 'auth_provider_test.mocks.dart';

void main() {
  late MockApiService mockApiService;
  late AuthProvider authProvider;

  setUp(() {
    mockApiService = MockApiService();
    authProvider = AuthProvider();
    // Inject the mock API service
    authProvider.apiService = mockApiService;
  });

  group('AuthProvider Signup Tests', () {
    final testSignupData = {
      'firstName': 'John',
      'lastName': 'Doe',
      'email': '<EMAIL>',
      'password': 'password123',
      'phone': '**********',
      'accountNumber': '**********',
      'accountName': '<PERSON>',
      'bankName': 'Test Bank',
      'state': 'Test State',
    };

    test('signup should return success when API call succeeds', () async {
      // Arrange
      final successResponse = ApiResponse<Map<String, dynamic>>(
        success: true,
        data: {
          'responseCode': '00',
          'responseMsg': 'Registration successful',
          'responseDesc': 'User registered successfully',
          'data': {'userId': 123, 'userRole': 'user'}
        },
      );

      when(mockApiService.post(
        ApiEndpoints.register,
        body: {
          'first_name': testSignupData['firstName'],
          'last_name': testSignupData['lastName'],
          'email': testSignupData['email'],
          'password': testSignupData['password'],
          'phone': testSignupData['phone'],
          'accountNumber': testSignupData['accountNumber'],
          'accountName': testSignupData['accountName'],
          'bankName': testSignupData['bankName'],
          'state': testSignupData['state'],
        },
        requiresAuth: false,
      )).thenAnswer((_) async => successResponse);

      // Act
      final result = await authProvider.signup(
        firstName: testSignupData['firstName']!,
        lastName: testSignupData['lastName']!,
        email: testSignupData['email']!,
        password: testSignupData['password']!,
        phone: testSignupData['phone']!,
        accountNumber: testSignupData['accountNumber']!,
        accountName: testSignupData['accountName']!,
        bankName: testSignupData['bankName']!,
        state: testSignupData['state']!,
      );

      // Assert
      expect(result['success'], true);
      expect(result['message'], 'Registration successful');
      expect(authProvider.isLoading, false);
      expect(authProvider.error, null);
    });

    test('signup should return failure when API call fails', () async {
      // Arrange
      final errorResponse = ApiResponse<Map<String, dynamic>>(
        success: false,
        error: 'Email already exists',
      );

      when(mockApiService.post(
        ApiEndpoints.register,
        body: {
          'first_name': testSignupData['firstName'],
          'last_name': testSignupData['lastName'],
          'email': testSignupData['email'],
          'password': testSignupData['password'],
          'phone': testSignupData['phone'],
          'accountNumber': testSignupData['accountNumber'],
          'accountName': testSignupData['accountName'],
          'bankName': testSignupData['bankName'],
          'state': testSignupData['state'],
        },
        requiresAuth: false,
      )).thenAnswer((_) async => errorResponse);

      // Act
      final result = await authProvider.signup(
        firstName: testSignupData['firstName']!,
        lastName: testSignupData['lastName']!,
        email: testSignupData['email']!,
        password: testSignupData['password']!,
        phone: testSignupData['phone']!,
        accountNumber: testSignupData['accountNumber']!,
        accountName: testSignupData['accountName']!,
        bankName: testSignupData['bankName']!,
        state: testSignupData['state']!,
      );

      // Assert
      expect(result['success'], false);
      expect(result['message'], 'Email already exists');
      expect(authProvider.isLoading, false);
      expect(authProvider.error, 'Email already exists');
    });

    test('signup should handle exceptions properly', () async {
      // Arrange
      when(mockApiService.post(
        ApiEndpoints.register,
        body: {
          'first_name': testSignupData['firstName'],
          'last_name': testSignupData['lastName'],
          'email': testSignupData['email'],
          'password': testSignupData['password'],
          'phone': testSignupData['phone'],
          'accountNumber': testSignupData['accountNumber'],
          'accountName': testSignupData['accountName'],
          'bankName': testSignupData['bankName'],
          'state': testSignupData['state'],
        },
        requiresAuth: false,
      )).thenThrow(Exception('Network error'));

      // Act
      final result = await authProvider.signup(
        firstName: testSignupData['firstName']!,
        lastName: testSignupData['lastName']!,
        email: testSignupData['email']!,
        password: testSignupData['password']!,
        phone: testSignupData['phone']!,
        accountNumber: testSignupData['accountNumber']!,
        accountName: testSignupData['accountName']!,
        bankName: testSignupData['bankName']!,
        state: testSignupData['state']!,
      );

      // Assert
      expect(result['success'], false);
      expect(
          result['message'], 'An unexpected error occurred. Please try again.');
      expect(authProvider.isLoading, false);
      expect(authProvider.error,
          'An unexpected error occurred. Please try again.');
    });

    test(
        'signup should handle API response with error code despite 200 HTTP status',
        () async {
      // Arrange - API returns 200 HTTP status but with error response code
      final errorResponse = ApiResponse<Map<String, dynamic>>(
        success: false,
        error:
            'Validation failed for argument [0]: [Field error in object \'userSignupReq\' on field \'email\': rejected value [string]; Email should be valid]',
        data: {
          'responseCode': '55',
          'responseMsg': 'Failed',
          'responseDesc':
              'Validation failed for argument [0]: [Field error in object \'userSignupReq\' on field \'email\': rejected value [string]; Email should be valid]',
        },
      );

      when(mockApiService.post(
        ApiEndpoints.register,
        body: {
          'first_name': testSignupData['firstName'],
          'last_name': testSignupData['lastName'],
          'email': testSignupData['email'],
          'password': testSignupData['password'],
          'phone': testSignupData['phone'],
          'accountNumber': testSignupData['accountNumber'],
          'accountName': testSignupData['accountName'],
          'bankName': testSignupData['bankName'],
          'state': testSignupData['state'],
        },
        requiresAuth: false,
      )).thenAnswer((_) async => errorResponse);

      // Act
      final result = await authProvider.signup(
        firstName: testSignupData['firstName']!,
        lastName: testSignupData['lastName']!,
        email: testSignupData['email']!,
        password: testSignupData['password']!,
        phone: testSignupData['phone']!,
        accountNumber: testSignupData['accountNumber']!,
        accountName: testSignupData['accountName']!,
        bankName: testSignupData['bankName']!,
        state: testSignupData['state']!,
      );

      // Assert
      expect(result['success'], false);
      expect(result['message'], contains('Validation failed'));
      expect(result['message'], contains('email'));
      expect(authProvider.isLoading, false);
      expect(authProvider.error, contains('Validation failed'));
      expect(result['data'], isNotNull); // Should include the raw response data
    });
  });

  group('AuthProvider Logout Tests', () {
    test('logout should call API and return success when API call succeeds',
        () async {
      // Arrange
      // First login to set up the user with email
      final loginResponse = ApiResponse<Map<String, dynamic>>(
        success: true,
        data: {
          'responseCode': '00',
          'responseMsg': 'Login successful',
          'data': {'jwt': 'test_token', 'userId': 123, 'userRole': 'user'}
        },
      );

      when(mockApiService.post(
        ApiEndpoints.login,
        body: {
          'email': '<EMAIL>',
          'password': 'password123',
        },
        requiresAuth: false,
      )).thenAnswer((_) async => loginResponse);

      when(mockApiService.saveToken(any)).thenAnswer((_) async {});

      // Login to set up the user
      await authProvider.login('<EMAIL>', 'password123');

      // Now set up the logout response
      final successResponse = ApiResponse<Map<String, dynamic>>(
        success: true,
        data: {
          'responseCode': '00',
          'responseMsg': 'Logout successful',
          'responseDesc': 'User logged out successfully',
          'data': null
        },
      );

      when(mockApiService.post(
        ApiEndpoints.logout,
        body: {
          'email': '<EMAIL>',
        },
        requiresAuth: true,
      )).thenAnswer((_) async => successResponse);

      // Mock the clearToken method
      when(mockApiService.clearToken()).thenAnswer((_) async {});

      // Act
      final result = await authProvider.logout();

      // Assert
      expect(result['success'], true);
      expect(result['message'], 'Logout successful');
      expect(authProvider.isLoading, false);
      expect(authProvider.user, isNull); // User should be cleared

      // Verify API was called
      verify(mockApiService.post(
        ApiEndpoints.logout,
        body: {
          'email': '<EMAIL>',
        },
        requiresAuth: true,
      )).called(1);

      // Verify token was cleared
      verify(mockApiService.clearToken()).called(1);
    });

    test('logout should handle API errors gracefully', () async {
      // Arrange
      // First login to set up the user with email
      final loginResponse = ApiResponse<Map<String, dynamic>>(
        success: true,
        data: {
          'responseCode': '00',
          'responseMsg': 'Login successful',
          'data': {'jwt': 'test_token', 'userId': 123, 'userRole': 'user'}
        },
      );

      when(mockApiService.post(
        ApiEndpoints.login,
        body: {
          'email': '<EMAIL>',
          'password': 'password123',
        },
        requiresAuth: false,
      )).thenAnswer((_) async => loginResponse);

      when(mockApiService.saveToken(any)).thenAnswer((_) async {});

      // Login to set up the user
      await authProvider.login('<EMAIL>', 'password123');

      // Now set up the logout error response
      final errorResponse = ApiResponse<Map<String, dynamic>>(
        success: false,
        error: 'Invalid token',
        data: {
          'responseCode': '55',
          'responseMsg': 'Failed',
          'responseDesc': 'Invalid token',
        },
      );

      when(mockApiService.post(
        ApiEndpoints.logout,
        body: {
          'email': '<EMAIL>',
        },
        requiresAuth: true,
      )).thenAnswer((_) async => errorResponse);

      // Mock the clearToken method
      when(mockApiService.clearToken()).thenAnswer((_) async {});

      // Act
      final result = await authProvider.logout();

      // Assert
      expect(result['success'],
          true); // Still considered success since local logout happened
      expect(result['message'], contains('Logged out locally'));
      expect(result['apiSuccess'], false);
      expect(authProvider.isLoading, false);
      expect(authProvider.user, isNull); // User should be cleared

      // Verify API was called
      verify(mockApiService.post(
        ApiEndpoints.logout,
        body: {
          'email': '<EMAIL>',
        },
        requiresAuth: true,
      )).called(1);

      // Verify token was cleared
      verify(mockApiService.clearToken()).called(1);
    });

    test('logout should handle exceptions gracefully', () async {
      // Arrange
      // First login to set up the user with email
      final loginResponse = ApiResponse<Map<String, dynamic>>(
        success: true,
        data: {
          'responseCode': '00',
          'responseMsg': 'Login successful',
          'data': {'jwt': 'test_token', 'userId': 123, 'userRole': 'user'}
        },
      );

      when(mockApiService.post(
        ApiEndpoints.login,
        body: {
          'email': '<EMAIL>',
          'password': 'password123',
        },
        requiresAuth: false,
      )).thenAnswer((_) async => loginResponse);

      when(mockApiService.saveToken(any)).thenAnswer((_) async {});

      // Login to set up the user
      await authProvider.login('<EMAIL>', 'password123');

      // Now set up the logout to throw an exception
      when(mockApiService.post(
        ApiEndpoints.logout,
        body: {
          'email': '<EMAIL>',
        },
        requiresAuth: true,
      )).thenThrow(Exception('Network error'));

      // Mock the clearToken method
      when(mockApiService.clearToken()).thenAnswer((_) async {});

      // Act
      final result = await authProvider.logout();

      // Assert
      expect(result['success'],
          true); // Still considered success since local logout happened
      expect(result['message'], contains('Logged out locally'));
      expect(result['error'], contains('Network error'));
      expect(authProvider.isLoading, false);
      expect(authProvider.user, isNull); // User should be cleared

      // Verify API was called
      verify(mockApiService.post(
        ApiEndpoints.logout,
        body: {
          'email': '<EMAIL>',
        },
        requiresAuth: true,
      )).called(1);

      // Verify token was cleared
      verify(mockApiService.clearToken()).called(1);
    });

    test('logout should handle case when user is null', () async {
      // Arrange - ensure user is null
      authProvider = AuthProvider(); // Create a fresh provider with no user
      authProvider.apiService = mockApiService;

      // Mock the clearToken method
      when(mockApiService.clearToken()).thenAnswer((_) async {});

      // Act
      final result = await authProvider.logout();

      // Assert
      expect(result['success'], true);
      expect(result['message'], 'Logged out locally');
      expect(result['apiCalled'], false);
      expect(authProvider.isLoading, false);
      expect(authProvider.user, isNull); // User should be cleared

      // Verify API was NOT called with the logout endpoint
      verifyNever(mockApiService.post(
        ApiEndpoints.logout,
        body: anyNamed('body'),
        requiresAuth: anyNamed('requiresAuth'),
      ));

      // Verify token was cleared
      verify(mockApiService.clearToken()).called(1);
    });
  });

  group('AuthProvider Authentication Persistence Tests', () {
    test('checkAuthentication should return false when no token exists',
        () async {
      // Arrange
      when(mockApiService.getToken()).thenAnswer((_) async => null);

      // Act
      final result = await authProvider.checkAuthentication();

      // Assert
      expect(result, false);
      expect(authProvider.isLoading, false);
      expect(authProvider.user, isNull);

      // Verify
      verify(mockApiService.getToken()).called(1);
    });

    test('checkAuthentication should return false when token is expired',
        () async {
      // Arrange
      when(mockApiService.getToken()).thenAnswer((_) async => null);

      // Act
      final result = await authProvider.checkAuthentication();

      // Assert
      expect(result, false);
      expect(authProvider.isLoading, false);
      expect(authProvider.user, isNull);

      // Verify
      verify(mockApiService.getToken()).called(1);
    });

    test(
        'checkAuthentication should restore user from cached data and validate token',
        () async {
      // Arrange
      when(mockApiService.getToken()).thenAnswer((_) async => 'valid_token');
      when(mockApiService.isTokenExpired()).thenAnswer((_) async => false);

      when(mockApiService.getUserData()).thenAnswer((_) async =>
          {'userId': 123, 'userRole': 'user', 'email': '<EMAIL>'});

      final successResponse = ApiResponse<Map<String, dynamic>>(
        success: true,
        data: {
          'responseCode': '00',
          'responseMsg': 'Success',
          'data': {
            'userId': 123,
            'userRole': 'user',
            'email': '<EMAIL>'
          }
        },
      );

      when(mockApiService.get(
        ApiEndpoints.userProfile,
        requiresAuth: true,
      )).thenAnswer((_) async => successResponse);

      // Act
      final result = await authProvider.checkAuthentication();

      // Assert
      expect(result, true);
      expect(authProvider.isLoading, false);
      expect(authProvider.user, isNotNull);
      expect(authProvider.user!.userId, 123);
      expect(authProvider.user!.userRole, 'user');
      expect(authProvider.user!.email, '<EMAIL>');

      // Verify
      verify(mockApiService.getToken()).called(1);
      verify(mockApiService.getUserData()).called(1);
      verify(mockApiService.get(
        ApiEndpoints.userProfile,
        requiresAuth: true,
      )).called(1);
      verify(mockApiService.saveUserData(any)).called(1);
    });

    test('checkAuthentication should handle token validation failure',
        () async {
      // Arrange
      when(mockApiService.getToken()).thenAnswer((_) async => 'invalid_token');
      when(mockApiService.isTokenExpired()).thenAnswer((_) async => false);

      when(mockApiService.getUserData()).thenAnswer((_) async =>
          {'userId': 123, 'userRole': 'user', 'email': '<EMAIL>'});

      final errorResponse = ApiResponse<Map<String, dynamic>>(
        success: false,
        error: 'Invalid token',
        data: null,
      );

      when(mockApiService.get(
        ApiEndpoints.userProfile,
        requiresAuth: true,
      )).thenAnswer((_) async => errorResponse);

      when(mockApiService.clearToken()).thenAnswer((_) async {});
      when(mockApiService.clearUserData()).thenAnswer((_) async {});

      // Act
      final result = await authProvider.checkAuthentication();

      // Assert
      expect(result, false);
      expect(authProvider.isLoading, false);
      expect(authProvider.user, isNull);

      // Verify
      verify(mockApiService.getToken()).called(1);
      verify(mockApiService.getUserData()).called(1);
      verify(mockApiService.get(
        ApiEndpoints.userProfile,
        requiresAuth: true,
      )).called(1);
      verify(mockApiService.clearToken()).called(1);
      verify(mockApiService.clearUserData()).called(1);
    });

    test('checkAuthentication should handle exceptions gracefully', () async {
      // Arrange
      when(mockApiService.getToken()).thenAnswer((_) async => 'valid_token');
      when(mockApiService.isTokenExpired()).thenAnswer((_) async => false);

      when(mockApiService.getUserData()).thenAnswer((_) async => null);

      when(mockApiService.get(
        ApiEndpoints.userProfile,
        requiresAuth: true,
      )).thenThrow(Exception('Network error'));

      // Act
      final result = await authProvider.checkAuthentication();

      // Assert
      expect(result, false);
      expect(authProvider.isLoading, false);

      // Verify
      verify(mockApiService.getToken()).called(1);
      verify(mockApiService.getUserData()).called(1);
      verify(mockApiService.get(
        ApiEndpoints.userProfile,
        requiresAuth: true,
      )).called(1);
    });
  });
}
