import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:mialamobile/api/api_service.dart';
import 'package:mialamobile/api/endpoints.dart';
import 'package:mialamobile/providers/auth_provider.dart';

@GenerateMocks([ApiService])
import 'reset_password_test.mocks.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('Forgot Password Tests', () {
    late MockApiService mockApiService;
    late AuthProvider authProvider;

    setUp(() {
      mockApiService = MockApiService();
      authProvider = AuthProvider();
      authProvider.apiService = mockApiService;
    });

    test('forgotPassword should send correct data to API', () async {
      // Arrange
      const email = '<EMAIL>';

      final successResponse = ApiResponse<Map<String, dynamic>>(
        success: true,
        data: {
          'responseCode': '00',
          'responseMsg': 'Reset instructions sent',
          'responseDesc':
              'Password reset instructions have been sent to your email',
          'data': null
        },
      );

      when(mockApiService.post(
        ApiEndpoints.forgotPassword,
        body: {
          'email': email,
        },
        requiresAuth: false,
      )).thenAnswer((_) async => successResponse);

      // Act
      final result = await authProvider.forgotPassword(
        email: email,
      );

      // Assert
      verify(mockApiService.post(
        ApiEndpoints.forgotPassword,
        body: {
          'email': email,
        },
        requiresAuth: false,
      )).called(1);

      expect(result['success'], true);
      expect(result['message'], 'Reset instructions sent');
    });

    test('forgotPassword should handle error response', () async {
      // Arrange
      const email = '<EMAIL>';

      final errorResponse = ApiResponse<Map<String, dynamic>>(
        success: true,
        data: {
          'responseCode': '01',
          'responseMsg': 'Email not found',
          'responseDesc': 'No account found with this email address',
          'data': null
        },
      );

      when(mockApiService.post(
        ApiEndpoints.forgotPassword,
        body: {
          'email': email,
        },
        requiresAuth: false,
      )).thenAnswer((_) async => errorResponse);

      // Act
      final result = await authProvider.forgotPassword(
        email: email,
      );

      // Assert
      verify(mockApiService.post(
        ApiEndpoints.forgotPassword,
        body: {
          'email': email,
        },
        requiresAuth: false,
      )).called(1);

      expect(result['success'], false);
      expect(result['message'], 'Email not found');
    });
  });

  group('Reset Password Tests', () {
    late MockApiService mockApiService;
    late AuthProvider authProvider;

    setUp(() {
      mockApiService = MockApiService();
      authProvider = AuthProvider();
      authProvider.apiService = mockApiService;
    });

    test('resetPassword should send correct data to API', () async {
      // Arrange
      const token = 'reset-token-123';
      const newPassword = 'newPassword123';
      const email = '<EMAIL>';

      final successResponse = ApiResponse<Map<String, dynamic>>(
        success: true,
        data: {
          'responseCode': '00',
          'responseMsg': 'Password reset successful',
          'responseDesc': 'Your password has been reset successfully',
          'data': null
        },
      );

      when(mockApiService.post(
        ApiEndpoints.resetPassword,
        body: {
          'email': email,
          'newPassword': newPassword,
        },
        requiresAuth: false,
      )).thenAnswer((_) async => successResponse);

      // Act
      final result = await authProvider.resetPassword(
        token: token, // token is still required by the method signature
        newPassword: newPassword,
        email: email,
      );

      // Assert
      verify(mockApiService.post(
        ApiEndpoints.resetPassword,
        body: {
          'email': email,
          'newPassword': newPassword,
        },
        requiresAuth: false,
      )).called(1);

      expect(result['success'], true);
      expect(result['message'], 'Password reset successful');
    });

    test('resetPassword should handle error response', () async {
      // Arrange
      const token = 'reset-token-123';
      const newPassword = 'newPassword123';
      const email = '<EMAIL>';

      final errorResponse = ApiResponse<Map<String, dynamic>>(
        success: true,
        data: {
          'responseCode': '01',
          'responseMsg': 'Invalid email',
          'responseDesc': 'The email is invalid or not registered',
          'data': null
        },
      );

      when(mockApiService.post(
        ApiEndpoints.resetPassword,
        body: {
          'email': email,
          'newPassword': newPassword,
        },
        requiresAuth: false,
      )).thenAnswer((_) async => errorResponse);

      // Act
      final result = await authProvider.resetPassword(
        token: token, // token is still required by the method signature
        newPassword: newPassword,
        email: email,
      );

      // Assert
      verify(mockApiService.post(
        ApiEndpoints.resetPassword,
        body: {
          'email': email,
          'newPassword': newPassword,
        },
        requiresAuth: false,
      )).called(1);

      expect(result['success'], false);
      expect(result['message'], 'Invalid email');
    });

    test('resetPassword should handle network errors', () async {
      // Arrange
      const token = 'reset-token-123';
      const newPassword = 'newPassword123';
      const email = '<EMAIL>';

      when(mockApiService.post(
        ApiEndpoints.resetPassword,
        body: {
          'email': email,
          'newPassword': newPassword,
        },
        requiresAuth: false,
      )).thenAnswer((_) async => ApiResponse<Map<String, dynamic>>(
            success: false,
            error: 'Network error',
          ));

      // Act
      final result = await authProvider.resetPassword(
        token: token, // token is still required by the method signature
        newPassword: newPassword,
        email: email,
      );

      // Assert
      verify(mockApiService.post(
        ApiEndpoints.resetPassword,
        body: {
          'email': email,
          'newPassword': newPassword,
        },
        requiresAuth: false,
      )).called(1);

      expect(result['success'], false);
      expect(result['message'], 'Network error');
    });

    test('resetPassword should require email', () async {
      // Arrange
      const token = 'reset-token-123';
      const newPassword = 'newPassword123';

      // Act
      final result = await authProvider.resetPassword(
        token: token,
        newPassword: newPassword,
        // No email provided
      );

      // Assert
      // Verify that no API call was made
      verifyNever(mockApiService.post(
        ApiEndpoints.resetPassword,
        body: anyNamed('body'),
        requiresAuth: anyNamed('requiresAuth'),
      ));

      expect(result['success'], false);
      expect(result['message'], 'Email is required for password reset');
    });
  });
}
