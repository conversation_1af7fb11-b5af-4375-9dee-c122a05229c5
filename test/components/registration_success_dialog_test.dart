import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mialamobile/components/registration_success_dialog.dart';
import 'package:mialamobile/providers/auth_provider.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:provider/provider.dart';

import 'registration_success_dialog_test.mocks.dart';

@GenerateMocks([AuthProvider])
void main() {
  late MockAuthProvider mockAuthProvider;

  setUp(() {
    mockAuthProvider = MockAuthProvider();
  });

  testWidgets('Registration success dialog displays correctly', (WidgetTester tester) async {
    // Stub the clearAuthenticationForPendingApproval method
    when(mockAuthProvider.clearAuthenticationForPendingApproval())
        .thenAnswer((_) async => {});

    // Build the dialog
    await tester.pumpWidget(
      MaterialApp(
        routes: {
          '/onboarding': (context) => const Scaffold(body: Text('Login Page')),
        },
        home: ChangeNotifierProvider<AuthProvider>.value(
          value: mockAuthProvider,
          child: const Scaffold(
            body: RegistrationSuccessDialog(),
          ),
        ),
      ),
    );

    // Verify that the dialog content is displayed
    expect(find.text('Registration Successful!'), findsOneWidget);
    expect(find.text('Your account has been created successfully. Please wait while an admin reviews and approves your registration. You will be notified once your account is approved.'), findsOneWidget);
    expect(find.text('Got it'), findsOneWidget);

    // Verify the success icon is displayed
    expect(find.byIcon(Icons.check), findsOneWidget);
  });

  testWidgets('Registration success dialog handles Got it button press', (WidgetTester tester) async {
    // Stub the clearAuthenticationForPendingApproval method
    when(mockAuthProvider.clearAuthenticationForPendingApproval())
        .thenAnswer((_) async => {});

    // Build the dialog
    await tester.pumpWidget(
      MaterialApp(
        routes: {
          '/onboarding': (context) => const Scaffold(body: Text('Login Page')),
        },
        home: ChangeNotifierProvider<AuthProvider>.value(
          value: mockAuthProvider,
          child: const Scaffold(
            body: RegistrationSuccessDialog(),
          ),
        ),
      ),
    );

    // Tap the "Got it" button
    await tester.tap(find.text('Got it'));
    await tester.pump();

    // Verify that the clearAuthenticationForPendingApproval method was called
    verify(mockAuthProvider.clearAuthenticationForPendingApproval()).called(1);
  });

  testWidgets('showRegistrationSuccessDialog function works correctly', (WidgetTester tester) async {
    // Stub the clearAuthenticationForPendingApproval method
    when(mockAuthProvider.clearAuthenticationForPendingApproval())
        .thenAnswer((_) async => {});

    // Build a test widget that can show the dialog
    await tester.pumpWidget(
      MaterialApp(
        routes: {
          '/onboarding': (context) => const Scaffold(body: Text('Login Page')),
        },
        home: ChangeNotifierProvider<AuthProvider>.value(
          value: mockAuthProvider,
          child: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () => showRegistrationSuccessDialog(context),
                child: const Text('Show Dialog'),
              ),
            ),
          ),
        ),
      ),
    );

    // Tap the button to show the dialog
    await tester.tap(find.text('Show Dialog'));
    await tester.pump();

    // Verify that the dialog is displayed
    expect(find.text('Registration Successful!'), findsOneWidget);
    expect(find.text('Got it'), findsOneWidget);
  });
}
