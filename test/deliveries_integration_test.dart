import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:mialamobile/models/delivery.dart';
import 'package:mialamobile/api/endpoints.dart';
import 'package:mialamobile/utils/delivery_utils.dart';

void main() {
  group('Deliveries Integration Tests', () {
    test('Delivery model should parse JSON correctly', () {
      // Test data based on the expected API response
      final testData = {
        'id': '123',
        'productName': 'iPhone 15 Pro Max',
        'qty': 2,
        'productPrice': 500000.0,
        'deliveryFee': 5000.0,
        'receiverPhone': '+2348012345678',
        'receiverName': '<PERSON>',
        'receiverAddress': '123 Main Street, Lagos',
        'deliveryCode': 'MLA-34-D6D976',
        'deliveryStatus': 'PENDING',
        'paymentStatus': 'NOT_PAID',
        'dueDate': '2024-03-15',
        'uploadDate': [2024, 3, 10],
      };

      final delivery = Delivery.fromJson(testData);

      expect(delivery.id, equals('123'));
      expect(delivery.productName, equals('iPhone 15 Pro Max'));
      expect(delivery.qty, equals(2));
      expect(delivery.productPrice, equals(500000.0));
      expect(delivery.deliveryFee, equals(5000.0));
      expect(delivery.receiverPhone, equals('+2348012345678'));
      expect(delivery.receiverName, equals('John Doe'));
      expect(delivery.receiverAddress, equals('123 Main Street, Lagos'));
      expect(delivery.deliveryCode, equals('MLA-34-D6D976'));
      expect(delivery.deliveryStatus, equals('PENDING'));
      expect(delivery.paymentStatus, equals('NOT_PAID'));
      expect(delivery.dueDate, equals('2024-03-15'));
      expect(delivery.uploadDate, equals([2024, 3, 10]));
    });

    test('Delivery model should handle missing fields with defaults', () {
      // Test data with missing fields
      final testData = <String, dynamic>{};

      final delivery = Delivery.fromJson(testData);

      expect(delivery.id, equals(''));
      expect(delivery.productName, equals(''));
      expect(delivery.qty, equals(0));
      expect(delivery.productPrice, equals(0.0));
      expect(delivery.deliveryFee, equals(0.0));
      expect(delivery.receiverPhone, equals(''));
      expect(delivery.receiverName, equals(''));
      expect(delivery.receiverAddress, equals(''));
      expect(delivery.deliveryCode, equals(''));
      expect(delivery.deliveryStatus, equals(''));
      expect(delivery.paymentStatus, equals(''));
      expect(delivery.dueDate, equals(''));
      expect(delivery.uploadDate, equals([]));
    });

    test('Delivery model should handle string numeric values from API', () {
      // Test data with string numeric values (as returned by the API)
      final testData = {
        'id': '123',
        'productName': 'iPhone 15 Pro Max',
        'qty': '2', // String instead of int
        'productPrice': '500000.0', // String instead of double
        'deliveryFee': '5000', // String instead of double
        'receiverPhone': '+2348012345678',
        'receiverName': 'John Doe',
        'receiverAddress': '123 Main Street, Lagos',
        'deliveryCode': 'MLA-34-D6D976',
        'deliveryStatus': 'PENDING',
        'paymentStatus': 'NOT_PAID',
        'dueDate': '2024-03-15',
        'uploadDate': [2024, 3, 10],
      };

      final delivery = Delivery.fromJson(testData);

      expect(delivery.id, equals('123'));
      expect(delivery.productName, equals('iPhone 15 Pro Max'));
      expect(delivery.qty, equals(2)); // Should be parsed as int
      expect(delivery.productPrice,
          equals(500000.0)); // Should be parsed as double
      expect(
          delivery.deliveryFee, equals(5000.0)); // Should be parsed as double
      expect(delivery.receiverPhone, equals('+2348012345678'));
      expect(delivery.receiverName, equals('John Doe'));
      expect(delivery.receiverAddress, equals('123 Main Street, Lagos'));
      expect(delivery.deliveryCode, equals('MLA-34-D6D976'));
      expect(delivery.deliveryStatus, equals('PENDING'));
      expect(delivery.paymentStatus, equals('NOT_PAID'));
      expect(delivery.dueDate, equals('2024-03-15'));
      expect(delivery.uploadDate, equals([2024, 3, 10]));
    });

    test('Delivery model should handle mixed data types gracefully', () {
      // Test data with mixed data types
      final testData = {
        'id': '456',
        'productName': 'Samsung Galaxy S24',
        'qty': 3, // int
        'productPrice': '750000', // string
        'deliveryFee': 7500.0, // double
        'receiverPhone': '+2348087654321',
        'receiverName': 'Jane Smith',
        'receiverAddress': '456 Oak Avenue, Abuja',
        'deliveryCode': 'MLA-56-A8B123',
        'deliveryStatus': 'DELIVERED',
        'paymentStatus': 'PAID',
        'dueDate': '2024-03-20',
        'uploadDate': ['2024', '3', '15'], // string array
      };

      final delivery = Delivery.fromJson(testData);

      expect(delivery.id, equals('456'));
      expect(delivery.productName, equals('Samsung Galaxy S24'));
      expect(delivery.qty, equals(3)); // Should remain int
      expect(delivery.productPrice,
          equals(750000.0)); // Should be parsed from string
      expect(delivery.deliveryFee, equals(7500.0)); // Should remain double
      expect(delivery.receiverPhone, equals('+2348087654321'));
      expect(delivery.receiverName, equals('Jane Smith'));
      expect(delivery.receiverAddress, equals('456 Oak Avenue, Abuja'));
      expect(delivery.deliveryCode, equals('MLA-56-A8B123'));
      expect(delivery.deliveryStatus, equals('DELIVERED'));
      expect(delivery.paymentStatus, equals('PAID'));
      expect(delivery.dueDate, equals('2024-03-20'));
      expect(delivery.uploadDate,
          equals([2024, 3, 15])); // Should be parsed from strings
    });

    test('Delivery model should handle invalid numeric strings gracefully', () {
      // Test data with invalid numeric strings
      final testData = {
        'id': '789',
        'productName': 'MacBook Pro',
        'qty': 'invalid', // Invalid string
        'productPrice': 'not_a_number', // Invalid string
        'deliveryFee': '', // Empty string
        'receiverPhone': '+2348012345678',
        'receiverName': 'Test User',
        'receiverAddress': 'Test Address',
        'deliveryCode': 'MLA-78-C9D456',
        'deliveryStatus': 'PENDING',
        'paymentStatus': 'NOT_PAID',
        'dueDate': '2024-03-25',
        'uploadDate': [2024, 3, 20],
      };

      final delivery = Delivery.fromJson(testData);

      expect(delivery.id, equals('789'));
      expect(delivery.productName, equals('MacBook Pro'));
      expect(delivery.qty, equals(0)); // Should default to 0 for invalid string
      expect(delivery.productPrice,
          equals(0.0)); // Should default to 0.0 for invalid string
      expect(delivery.deliveryFee,
          equals(0.0)); // Should default to 0.0 for empty string
      expect(delivery.receiverPhone, equals('+2348012345678'));
      expect(delivery.receiverName, equals('Test User'));
      expect(delivery.receiverAddress, equals('Test Address'));
      expect(delivery.deliveryCode, equals('MLA-78-C9D456'));
      expect(delivery.deliveryStatus, equals('PENDING'));
      expect(delivery.paymentStatus, equals('NOT_PAID'));
      expect(delivery.dueDate, equals('2024-03-25'));
      expect(delivery.uploadDate, equals([2024, 3, 20]));
    });

    test('DeliveryUtils should format dates correctly', () {
      expect(DeliveryUtils.formatDeliveryDate('2024-03-15'), equals('15Mar'));
      expect(DeliveryUtils.formatDeliveryDate('2024-12-25'), equals('25Dec'));
      expect(DeliveryUtils.formatDeliveryDate('invalid-date'),
          equals('invalid-date'));
    });

    test('DeliveryUtils should format delivery time correctly', () {
      final time1 = DeliveryUtils.formatDeliveryTime([2024, 3, 15]);
      expect(time1, isNotEmpty);
      expect(time1, contains(RegExp(r'\d{1,2}:\d{2}(am|pm)')));

      final time2 = DeliveryUtils.formatDeliveryTime([]);
      expect(time2, equals('12:00pm'));
    });

    test('DeliveryUtils should return correct status colors', () {
      expect(DeliveryUtils.getStatusColor('DELIVERED'),
          equals(const Color(0xff153D80)));
      expect(DeliveryUtils.getStatusColor('PENDING'),
          equals(const Color(0xffFFA500)));
      expect(DeliveryUtils.getStatusColor('FAILED'),
          equals(const Color(0xffB10303)));
      expect(DeliveryUtils.getStatusColor('ASSIGNED'),
          equals(const Color(0xff4CAF50)));
      expect(DeliveryUtils.getStatusColor('UNKNOWN'),
          equals(const Color(0xff8C8C8C)));
    });

    test('DeliveryUtils should format delivery status correctly', () {
      expect(DeliveryUtils.formatDeliveryStatus('PENDING'), equals('Pending'));
      expect(DeliveryUtils.formatDeliveryStatus('IN_TRANSIT'),
          equals('In Transit'));
      expect(
          DeliveryUtils.formatDeliveryStatus('NOT_PAID'), equals('Not Paid'));
      expect(
          DeliveryUtils.formatDeliveryStatus('delivered'), equals('Delivered'));
    });

    test('DeliveryUtils should limit deliveries correctly', () {
      final deliveries = List.generate(10, (index) => 'delivery_$index');

      final limited = DeliveryUtils.limitToRecent(deliveries, limit: 5);
      expect(limited.length, equals(5));
      expect(
          limited,
          equals([
            'delivery_0',
            'delivery_1',
            'delivery_2',
            'delivery_3',
            'delivery_4'
          ]));

      final smallList = ['delivery_1', 'delivery_2'];
      final limitedSmall = DeliveryUtils.limitToRecent(smallList, limit: 5);
      expect(limitedSmall.length, equals(2));
      expect(limitedSmall, equals(smallList));
    });

    test('API endpoint should be correctly defined', () {
      expect(ApiEndpoints.deliveries, isNotEmpty);
      expect(ApiEndpoints.deliveries, contains('/rider/deliveries'));
    });

    test('Delivery model should handle actual API response format', () {
      // Test data that simulates the actual API response format that was causing the error
      final apiResponseData = {
        'id': '67890',
        'productName': 'iPad Pro',
        'qty': '1', // String from API
        'productPrice': '3', // String from API
        'deliveryFee': '3', // String from API
        'receiverPhone': '+2348012345678',
        'receiverName': 'API Test User',
        'receiverAddress': 'API Test Address',
        'deliveryCode': 'MLA-API-TEST',
        'deliveryStatus': 'PENDING',
        'paymentStatus': 'NOT_PAID',
        'dueDate': '2024-03-30',
        'uploadDate': [2024, 3, 25],
      };

      // This should not throw a type casting error
      final delivery = Delivery.fromJson(apiResponseData);

      expect(delivery.id, equals('67890'));
      expect(delivery.productName, equals('iPad Pro'));
      expect(
          delivery.qty, equals(1)); // Should be parsed from string "1" to int 1
      expect(delivery.productPrice,
          equals(3.0)); // Should be parsed from string "3" to double 3.0
      expect(delivery.deliveryFee,
          equals(3.0)); // Should be parsed from string "3" to double 3.0
      expect(delivery.receiverPhone, equals('+2348012345678'));
      expect(delivery.receiverName, equals('API Test User'));
      expect(delivery.receiverAddress, equals('API Test Address'));
      expect(delivery.deliveryCode, equals('MLA-API-TEST'));
      expect(delivery.deliveryStatus, equals('PENDING'));
      expect(delivery.paymentStatus, equals('NOT_PAID'));
      expect(delivery.dueDate, equals('2024-03-30'));
      expect(delivery.uploadDate, equals([2024, 3, 25]));
    });

    test('Delivery model should convert to JSON correctly', () {
      final delivery = Delivery(
        id: '123',
        productName: 'Test Product',
        qty: 1,
        productPrice: 1000.0,
        deliveryFee: 500.0,
        receiverPhone: '+2348012345678',
        receiverName: 'Test User',
        receiverAddress: 'Test Address',
        deliveryCode: 'TEST-123',
        deliveryStatus: 'PENDING',
        paymentStatus: 'NOT_PAID',
        dueDate: '2024-03-15',
        uploadDate: [2024, 3, 10],
      );

      final json = delivery.toJson();

      expect(json['id'], equals('123'));
      expect(json['productName'], equals('Test Product'));
      expect(json['qty'], equals(1));
      expect(json['deliveryCode'], equals('TEST-123'));
      expect(json['deliveryStatus'], equals('PENDING'));
    });
  });
}
