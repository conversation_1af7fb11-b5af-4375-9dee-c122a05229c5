import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:mialamobile/api/api_service.dart';
import 'package:mialamobile/providers/auth_provider.dart';
import 'package:mialamobile/onboarding/register/register.dart';
import 'package:mialamobile/components/input_type.dart';
import 'package:provider/provider.dart';

// Generate mock classes
@GenerateMocks([http.Client])
import 'signup_test.mocks.dart';

void main() {
  late MockClient mockClient;
  late ApiService apiService;
  late AuthProvider authProvider;

  setUp(() {
    mockClient = MockClient();
    apiService = ApiService();
    apiService.httpClient = mockClient;
    apiService.hasInternetConnectionOverride = true;

    authProvider = AuthProvider();
    authProvider.apiService = apiService;
  });

  group('Signup Form Validation', () {
    testWidgets('validates email format', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<AuthProvider>.value(
            value: authProvider,
            child: const RegistrationPage(),
          ),
        ),
      );

      // Find the email field and enter an invalid email
      final emailField = find.widgetWithText(InputType, 'Email');
      await tester.enterText(emailField, 'invalid-email');
      await tester.pump();

      // Trigger validation by unfocusing the field
      await tester.testTextInput.receiveAction(TextInputAction.done);
      await tester.pump();

      // Expect to find an error message
      expect(find.text('Please enter a valid email address'), findsOneWidget);
    });

    testWidgets('validates account number format', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<AuthProvider>.value(
            value: authProvider,
            child: const RegistrationPage(),
          ),
        ),
      );

      // Find the account number field and enter an invalid account number
      final accountNumberField =
          find.widgetWithText(InputType, 'Account Number');
      await tester.enterText(accountNumberField, '12345'); // Too short
      await tester.pump();

      // Trigger validation by unfocusing the field
      await tester.testTextInput.receiveAction(TextInputAction.done);
      await tester.pump();

      // Expect to find an error message
      expect(find.text('Please enter a valid 10-digit account number'),
          findsOneWidget);
    });

    testWidgets('validates phone number format', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<AuthProvider>.value(
            value: authProvider,
            child: const RegistrationPage(),
          ),
        ),
      );

      // Find the phone number field and enter an invalid phone number
      final phoneField = find.widgetWithText(InputType, 'Mobile Number');
      await tester.enterText(phoneField, '12345'); // Too short
      await tester.pump();

      // Trigger validation by unfocusing the field
      await tester.testTextInput.receiveAction(TextInputAction.done);
      await tester.pump();

      // Expect to find an error message
      expect(find.text('Please enter a valid 10-digit phone number'),
          findsOneWidget);
    });
  });

  group('Signup API Integration', () {
    test('handles successful signup', () async {
      // Mock a successful response
      when(mockClient.post(
        any,
        headers: anyNamed('headers'),
        body: anyNamed('body'),
      )).thenAnswer((_) async => http.Response(
            jsonEncode({
              'responseCode': '00',
              'responseMsg': 'Success',
              'responseDesc': 'Registration successful',
              'data': 'User registered successfully'
            }),
            200,
          ));

      final result = await authProvider.signup(
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'Password123',
        phone: '***********',
        accountNumber: '**********',
        accountName: 'John Doe',
        bankName: 'Test Bank',
        state: '**********1',
      );

      expect(result['success'], true);
      expect(result['message'], 'Success');
    });

    test('handles validation error response', () async {
      // Mock a validation error response
      when(mockClient.post(
        any,
        headers: anyNamed('headers'),
        body: anyNamed('body'),
      )).thenAnswer((_) async => http.Response(
            jsonEncode({
              'responseCode': '55',
              'responseMsg': 'Failed',
              'responseDesc':
                  'Validation failed for argument [0] in public org.springframework.http.ResponseEntity: default message [Email should be valid]'
            }),
            200,
          ));

      final result = await authProvider.signup(
        firstName: 'John',
        lastName: 'Doe',
        email: 'invalid-email',
        password: 'Password123',
        phone: '***********',
        accountNumber: '**********',
        accountName: 'John Doe',
        bankName: 'Test Bank',
        state: '**********1',
      );

      expect(result['success'], false);
      expect(result['message'], contains('Email should be valid'));
    });

    test('handles complex validation error response', () async {
      // Mock a complex validation error response with multiple errors
      when(mockClient.post(
        any,
        headers: anyNamed('headers'),
        body: anyNamed('body'),
      )).thenAnswer((_) async => http.Response(
            jsonEncode({
              'responseCode': '55',
              'responseMsg': 'Failed',
              'responseDesc':
                  'Validation failed for multiple fields: default message [Account number must be 10 digits]'
            }),
            200,
          ));

      final result = await authProvider.signup(
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'Password123',
        phone: '***********',
        accountNumber: '12345', // Invalid account number
        accountName: 'John Doe',
        bankName: 'Test Bank',
        state: '**********1',
      );

      expect(result['success'], false);
      expect(result['message'], contains('Account number must be 10 digits'));
    });

    test('handles network error', () async {
      // Mock a network error
      when(mockClient.post(
        any,
        headers: anyNamed('headers'),
        body: anyNamed('body'),
      )).thenThrow(Exception('Network error'));

      final result = await authProvider.signup(
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'Password123',
        phone: '***********',
        accountNumber: '**********',
        accountName: 'John Doe',
        bankName: 'Test Bank',
        state: '**********1',
      );

      expect(result['success'], false);
      expect(result['message'], contains('Network error'));
    });
  });
}
