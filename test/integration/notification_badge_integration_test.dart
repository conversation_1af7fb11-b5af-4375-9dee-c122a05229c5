import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:provider/provider.dart';
import 'package:mialamobile/components/notification_badge.dart';
import 'package:mialamobile/providers/auth_provider.dart';
import 'package:mialamobile/api/api_service.dart';
import 'package:mialamobile/models/notification.dart';
import 'package:mialamobile/api/models/user_model.dart';

// Use existing mocks from notifications test
import '../notifications_test.mocks.dart';

void main() {
  group('NotificationBadge Integration Tests', () {
    late AuthProvider authProvider;
    late MockApiService mockApiService;

    setUp(() {
      mockApiService = MockApiService();
      authProvider = AuthProvider();
      authProvider.apiService = mockApiService;
    });

    testWidgets(
        'should update badge count when notifications are marked as read',
        (WidgetTester tester) async {
      // Arrange
      final user =
          User(userId: 123, userRole: 'rider', email: '<EMAIL>');
      authProvider.setUserForTesting(user);

      // Add some unread notifications
      final notifications = [
        NotificationModel(
          id: 1,
          recipientId: 123,
          message: 'Test 1',
          read: false,
          createdAt: [2024, 1, 15, 10, 30, 0, 0],
          type: 'TEST',
        ),
        NotificationModel(
          id: 2,
          recipientId: 123,
          message: 'Test 2',
          read: false,
          createdAt: [2024, 1, 15, 10, 30, 0, 0],
          type: 'TEST',
        ),
        NotificationModel(
          id: 3,
          recipientId: 123,
          message: 'Test 3',
          read: true, // This one is already read
          createdAt: [2024, 1, 15, 10, 30, 0, 0],
          type: 'TEST',
        ),
      ];

      authProvider.notifications.addAll(notifications);

      // Mock successful API response for marking notification as read
      final mockResponse = {
        'responseCode': '00',
        'responseMsg': 'Success',
        'data': {}
      };

      when(mockApiService.post(any, requiresAuth: true)).thenAnswer(
          (_) async => ApiResponse<dynamic>(success: true, data: mockResponse));

      // Build widget with provider
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<AuthProvider>.value(
            value: authProvider,
            child: Scaffold(
              body: Consumer<AuthProvider>(
                builder: (context, authProvider, child) {
                  return NotificationBadge(
                    iconPath: 'assets/icons/notification.svg',
                    badgeCount: authProvider.unreadNotificationsCount,
                  );
                },
              ),
            ),
          ),
        ),
      );

      await tester.pump();

      // Assert initial state - should show badge with count 2 (2 unread notifications)
      expect(find.text('2'), findsOneWidget);

      // Act - Mark one notification as read
      await authProvider.markNotificationAsRead(1);
      await tester.pump();

      // Assert - Badge should now show count 1
      expect(find.text('1'), findsOneWidget);
      expect(find.text('2'), findsNothing);

      // Act - Mark the second notification as read
      await authProvider.markNotificationAsRead(2);
      await tester.pump();

      // Assert - Badge should be hidden (count = 0)
      expect(find.text('1'), findsNothing);
      expect(find.text('0'), findsNothing); // Badge is hidden when count is 0
    });

    testWidgets(
        'should show correct badge count when notifications are fetched',
        (WidgetTester tester) async {
      // Arrange
      final user =
          User(userId: 123, userRole: 'rider', email: '<EMAIL>');
      authProvider.setUserForTesting(user);

      // Mock API response for fetching notifications
      final mockResponse = [
        {
          'id': 1,
          'recipientId': 123,
          'message': 'New delivery assigned',
          'read': false,
          'createdAt': [2024, 1, 15, 10, 30, 0, 0],
          'type': 'DELIVERY_ASSIGNED',
        },
        {
          'id': 2,
          'recipientId': 123,
          'message': 'Payment received',
          'read': false,
          'createdAt': [2024, 1, 15, 10, 30, 0, 0],
          'type': 'PAYMENT_RECEIVED',
        },
        {
          'id': 3,
          'recipientId': 123,
          'message': 'Delivery completed',
          'read': true,
          'createdAt': [2024, 1, 15, 10, 30, 0, 0],
          'type': 'DELIVERY_COMPLETED',
        },
      ];

      when(mockApiService.get(any, requiresAuth: true)).thenAnswer(
          (_) async => ApiResponse<dynamic>(success: true, data: mockResponse));

      // Build widget with provider
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<AuthProvider>.value(
            value: authProvider,
            child: Scaffold(
              body: Consumer<AuthProvider>(
                builder: (context, authProvider, child) {
                  return NotificationBadge(
                    iconPath: 'assets/icons/notification.svg',
                    badgeCount: authProvider.unreadNotificationsCount,
                  );
                },
              ),
            ),
          ),
        ),
      );

      await tester.pump();

      // Initially no badge should be shown (no notifications loaded)
      expect(find.text('2'), findsNothing);

      // Act - Fetch notifications
      await authProvider.fetchNotifications();
      await tester.pump();

      // Assert - Badge should show count 2 (2 unread notifications)
      expect(find.text('2'), findsOneWidget);
    });

    testWidgets('should handle large notification counts correctly',
        (WidgetTester tester) async {
      // Arrange
      final user =
          User(userId: 123, userRole: 'rider', email: '<EMAIL>');
      authProvider.setUserForTesting(user);

      // Add many unread notifications
      for (int i = 1; i <= 15; i++) {
        authProvider.notifications.add(
          NotificationModel(
            id: i,
            recipientId: 123,
            message: 'Test $i',
            read: false,
            createdAt: [2024, 1, 15, 10, 30, 0, 0],
            type: 'TEST',
          ),
        );
      }

      // Build widget with provider
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<AuthProvider>.value(
            value: authProvider,
            child: Scaffold(
              body: Consumer<AuthProvider>(
                builder: (context, authProvider, child) {
                  return NotificationBadge(
                    iconPath: 'assets/icons/notification.svg',
                    badgeCount: authProvider.unreadNotificationsCount,
                  );
                },
              ),
            ),
          ),
        ),
      );

      await tester.pump();

      // Assert - Badge should show "9+" for counts > 9
      expect(find.text('9+'), findsOneWidget);
      expect(find.text('15'), findsNothing);
    });
  });
}
