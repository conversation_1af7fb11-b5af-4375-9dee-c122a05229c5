import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mialamobile/onboarding/login/otp_verification.dart';

void main() {
  testWidgets('OTP verification has registration success dialog implemented',
      (WidgetTester tester) async {
    // Build the OTP verification widget
    await tester.pumpWidget(
      const MaterialApp(
        home: OtpVerification(email: '<EMAIL>'),
      ),
    );

    // Get the widget state
    final state = tester.state<State>(find.byType(OtpVerification));

    // Verify that the widget has been created successfully
    expect(state, isNotNull);

    // This test simply verifies that the OtpVerification widget can be built
    // The actual registration success dialog is shown after successful OTP verification
    // which depends on API responses and is difficult to test in widget tests
  });
}
