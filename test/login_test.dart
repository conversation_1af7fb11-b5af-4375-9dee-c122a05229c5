import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:mialamobile/providers/auth_provider.dart';
import 'package:mialamobile/onboarding/login/login_page.dart';

void main() {
  late AuthProvider authProvider;

  setUp(() {
    authProvider = AuthProvider();
  });

  group('Login UI Tests', () {
    testWidgets('Login form validation works correctly',
        (WidgetTester tester) async {
      // Build the login page
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<AuthProvider>.value(
            value: authProvider,
            child: const LoginPage(),
          ),
        ),
      );

      // Wait for the widget to be fully built
      await tester.pumpAndSettle();

      // Find the email and password fields
      final emailField = find.byType(TextFormField).at(0);
      final passwordField = find.byType(TextFormField).at(1);

      // Find the login button
      final loginButton = find.widgetWithText(ElevatedButton, 'Login');
      expect(loginButton, findsOneWidget);

      // Initially, the login button should be disabled
      final buttonWidget = tester.widget<ElevatedButton>(loginButton);
      expect(buttonWidget.onPressed, isNull); // Null onPressed means disabled

      // Enter invalid email
      await tester.enterText(emailField, 'invalid-email');
      await tester.pump();

      // Enter valid password
      await tester.enterText(passwordField, 'password123');
      await tester.pump();

      // Button should still be disabled due to invalid email
      final buttonWidgetAfterInvalidEmail =
          tester.widget<ElevatedButton>(loginButton);
      expect(buttonWidgetAfterInvalidEmail.onPressed, isNull);

      // Enter valid email
      await tester.enterText(emailField, '<EMAIL>');
      await tester.pump();

      // Now button should be enabled
      final buttonWidgetAfterValidEmail =
          tester.widget<ElevatedButton>(loginButton);
      expect(buttonWidgetAfterValidEmail.onPressed, isNotNull);
    });
  });
}
