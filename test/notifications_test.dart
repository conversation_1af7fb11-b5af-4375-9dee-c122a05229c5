import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:mialamobile/models/notification.dart';
import 'package:mialamobile/providers/auth_provider.dart';
import 'package:mialamobile/api/api_service.dart';
import 'package:mialamobile/api/models/user_model.dart';

// Generate mocks
@GenerateMocks([ApiService])
import 'notifications_test.mocks.dart';

void main() {
  group('NotificationModel Tests', () {
    test('should create NotificationModel from JSON', () {
      // Arrange
      final json = {
        'id': 1,
        'recipientId': 123,
        'message': 'Test notification message',
        'read': false,
        'createdAt': [2024, 1, 15, 10, 30, 0, 0],
        'type': 'DELIVERY_ASSIGNED',
      };

      // Act
      final notification = NotificationModel.fromJson(json);

      // Assert
      expect(notification.id, 1);
      expect(notification.recipientId, 123);
      expect(notification.message, 'Test notification message');
      expect(notification.read, false);
      expect(notification.type, 'DELIVERY_ASSIGNED');
      expect(notification.createdAt, [2024, 1, 15, 10, 30, 0, 0]);
    });

    test('should format time correctly', () {
      // Arrange
      final notification = NotificationModel(
        id: 1,
        recipientId: 123,
        message: 'Test',
        read: false,
        createdAt: [2024, 1, 15, 14, 30, 0, 0], // 2:30 PM
        type: 'TEST',
      );

      // Act & Assert
      expect(notification.formattedTime, '02:30pm');
    });

    test('should format date correctly for today', () {
      // Arrange
      final now = DateTime.now();
      final notification = NotificationModel(
        id: 1,
        recipientId: 123,
        message: 'Test',
        read: false,
        createdAt: [now.year, now.month, now.day, 10, 30, 0, 0],
        type: 'TEST',
      );

      // Act & Assert
      expect(notification.formattedDate, 'Today');
    });

    test('should return correct icon path for notification type', () {
      // Arrange
      final notification = NotificationModel(
        id: 1,
        recipientId: 123,
        message: 'Test',
        read: false,
        createdAt: [2024, 1, 15, 10, 30, 0, 0],
        type: 'DELIVERY_ASSIGNED',
      );

      // Act & Assert
      expect(notification.iconPath, 'assets/icons/order.svg');
    });

    test('should create copy with updated read status', () {
      // Arrange
      final notification = NotificationModel(
        id: 1,
        recipientId: 123,
        message: 'Test',
        read: false,
        createdAt: [2024, 1, 15, 10, 30, 0, 0],
        type: 'TEST',
      );

      // Act
      final updatedNotification = notification.copyWith(read: true);

      // Assert
      expect(updatedNotification.read, true);
      expect(updatedNotification.id, notification.id);
      expect(updatedNotification.message, notification.message);
    });
  });

  group('AuthProvider Notifications Tests', () {
    late AuthProvider authProvider;
    late MockApiService mockApiService;

    setUp(() {
      mockApiService = MockApiService();
      authProvider = AuthProvider();
      authProvider.apiService = mockApiService;
    });

    test(
        'should fetch notifications successfully with standard response format',
        () async {
      // Arrange
      final user =
          User(userId: 123, userRole: 'rider', email: '<EMAIL>');
      authProvider.setUserForTesting(user); // We'll need to add this method

      final mockResponse = {
        'responseCode': '00',
        'responseMsg': 'Success',
        'data': [
          {
            'id': 1,
            'recipientId': 123,
            'message': 'Test notification',
            'read': false,
            'createdAt': [2024, 1, 15, 10, 30, 0, 0],
            'type': 'DELIVERY_ASSIGNED',
          }
        ]
      };

      when(mockApiService.get(any, requiresAuth: true)).thenAnswer(
          (_) async => ApiResponse<dynamic>(success: true, data: mockResponse));

      // Act
      final result = await authProvider.fetchNotifications();

      // Assert
      expect(result, true);
      expect(authProvider.notifications.length, 1);
      expect(authProvider.notifications.first.message, 'Test notification');
    });

    test(
        'should fetch notifications successfully with direct array response format',
        () async {
      // Arrange
      final user =
          User(userId: 123, userRole: 'rider', email: '<EMAIL>');
      authProvider.setUserForTesting(user);

      final mockResponse = [
        {
          'id': 19,
          'recipientId': 38,
          'message':
              'A new delivery (Code: MLA-34-E42194) has been assigned to you...',
          'read': false,
          'createdAt': [2025, 5, 29, 10, 6, 19, 342604000],
          'type': 'DELIVERY_ASSIGNED',
        }
      ];

      when(mockApiService.get(any, requiresAuth: true)).thenAnswer(
          (_) async => ApiResponse<dynamic>(success: true, data: mockResponse));

      // Act
      final result = await authProvider.fetchNotifications();

      // Assert
      expect(result, true);
      expect(authProvider.notifications.length, 1);
      expect(authProvider.notifications.first.message,
          'A new delivery (Code: MLA-34-E42194) has been assigned to you...');
      expect(authProvider.notifications.first.id, 19);
      expect(authProvider.notifications.first.recipientId, 38);
    });

    test('should handle fetch notifications error', () async {
      // Arrange
      final user =
          User(userId: 123, userRole: 'rider', email: '<EMAIL>');
      authProvider.setUserForTesting(user);

      when(mockApiService.get(any, requiresAuth: true)).thenAnswer(
          (_) async => ApiResponse(success: false, error: 'Network error'));

      // Act
      final result = await authProvider.fetchNotifications();

      // Assert
      expect(result, false);
      expect(authProvider.notificationsError, 'Network error');
    });

    test('should mark notification as read with successful API call', () async {
      // Arrange
      final user =
          User(userId: 123, userRole: 'rider', email: '<EMAIL>');
      authProvider.setUserForTesting(user);

      final notification = NotificationModel(
        id: 1,
        recipientId: 123,
        message: 'Test',
        read: false,
        createdAt: [2024, 1, 15, 10, 30, 0, 0],
        type: 'TEST',
      );

      authProvider.notifications.add(notification);

      // Mock successful API response
      final mockResponse = {
        'responseCode': '00',
        'responseMsg': 'Success',
        'data': {}
      };

      when(mockApiService.post(any, requiresAuth: true)).thenAnswer(
          (_) async => ApiResponse<dynamic>(success: true, data: mockResponse));

      // Act
      final result = await authProvider.markNotificationAsRead(1);

      // Assert
      expect(result, true);
      expect(authProvider.notifications.first.read, true);

      // Verify API was called with correct endpoint
      verify(mockApiService.post(
        'https://miala.onrender.com/api/v1/notify/notifications/mark-read/1',
        body: null,
        requiresAuth: true,
      )).called(1);
    });

    test('should fail to mark notification as read when API returns error',
        () async {
      // Arrange
      final user =
          User(userId: 123, userRole: 'rider', email: '<EMAIL>');
      authProvider.setUserForTesting(user);

      final notification = NotificationModel(
        id: 1,
        recipientId: 123,
        message: 'Test',
        read: false,
        createdAt: [2024, 1, 15, 10, 30, 0, 0],
        type: 'TEST',
      );

      authProvider.notifications.add(notification);

      // Mock API error response
      final mockResponse = {
        'responseCode': '01',
        'responseMsg': 'Failed to mark as read',
        'data': null
      };

      when(mockApiService.post(any, requiresAuth: true)).thenAnswer(
          (_) async => ApiResponse<dynamic>(success: true, data: mockResponse));

      // Act
      final result = await authProvider.markNotificationAsRead(1);

      // Assert
      expect(result, false);
      expect(authProvider.notifications.first.read,
          false); // Should remain unchanged

      // Verify API was called
      verify(mockApiService.post(
        'https://miala.onrender.com/api/v1/notify/notifications/mark-read/1',
        body: null,
        requiresAuth: true,
      )).called(1);
    });

    test('should fail to mark notification as read when API call fails',
        () async {
      // Arrange
      final user =
          User(userId: 123, userRole: 'rider', email: '<EMAIL>');
      authProvider.setUserForTesting(user);

      final notification = NotificationModel(
        id: 1,
        recipientId: 123,
        message: 'Test',
        read: false,
        createdAt: [2024, 1, 15, 10, 30, 0, 0],
        type: 'TEST',
      );

      authProvider.notifications.add(notification);

      // Mock API failure
      when(mockApiService.post(any, requiresAuth: true)).thenAnswer(
          (_) async => ApiResponse(success: false, error: 'Network error'));

      // Act
      final result = await authProvider.markNotificationAsRead(1);

      // Assert
      expect(result, false);
      expect(authProvider.notifications.first.read,
          false); // Should remain unchanged

      // Verify API was called
      verify(mockApiService.post(
        'https://miala.onrender.com/api/v1/notify/notifications/mark-read/1',
        body: null,
        requiresAuth: true,
      )).called(1);
    });

    test(
        'should fail to mark notification as read when user is not authenticated',
        () async {
      // Arrange - No user set
      final notification = NotificationModel(
        id: 1,
        recipientId: 123,
        message: 'Test',
        read: false,
        createdAt: [2024, 1, 15, 10, 30, 0, 0],
        type: 'TEST',
      );

      authProvider.notifications.add(notification);

      // Act
      final result = await authProvider.markNotificationAsRead(1);

      // Assert
      expect(result, false);
      expect(authProvider.notifications.first.read,
          false); // Should remain unchanged

      // Verify API was not called
      verifyNever(mockApiService.post(any, requiresAuth: true));
    });

    test('should fail to mark notification as read when notification not found',
        () async {
      // Arrange
      final user =
          User(userId: 123, userRole: 'rider', email: '<EMAIL>');
      authProvider.setUserForTesting(user);

      // Act - Try to mark non-existent notification
      final result = await authProvider.markNotificationAsRead(999);

      // Assert
      expect(result, false);

      // Verify API was not called
      verifyNever(mockApiService.post(any, requiresAuth: true));
    });

    test('should mark all notifications as read', () async {
      // Arrange
      final notifications = [
        NotificationModel(
            id: 1,
            recipientId: 123,
            message: 'Test 1',
            read: false,
            createdAt: [2024, 1, 15, 10, 30, 0, 0],
            type: 'TEST'),
        NotificationModel(
            id: 2,
            recipientId: 123,
            message: 'Test 2',
            read: false,
            createdAt: [2024, 1, 15, 10, 30, 0, 0],
            type: 'TEST'),
      ];

      authProvider.notifications.addAll(notifications);

      // Act
      final result = await authProvider.markAllNotificationsAsRead();

      // Assert
      expect(result, true);
      expect(authProvider.notifications.every((n) => n.read), true);
    });

    test('should filter notifications by search query', () {
      // Arrange
      final notifications = [
        NotificationModel(
            id: 1,
            recipientId: 123,
            message: 'Delivery assigned',
            read: false,
            createdAt: [2024, 1, 15, 10, 30, 0, 0],
            type: 'DELIVERY_ASSIGNED'),
        NotificationModel(
            id: 2,
            recipientId: 123,
            message: 'Payment received',
            read: false,
            createdAt: [2024, 1, 15, 10, 30, 0, 0],
            type: 'PAYMENT_RECEIVED'),
      ];

      authProvider.notifications.addAll(notifications);

      // Act
      final filtered = authProvider.filterNotifications('delivery');

      // Assert
      expect(filtered.length, 1);
      expect(filtered.first.message, 'Delivery assigned');
    });

    test('should return unread notifications count', () {
      // Arrange
      final notifications = [
        NotificationModel(
            id: 1,
            recipientId: 123,
            message: 'Test 1',
            read: false,
            createdAt: [2024, 1, 15, 10, 30, 0, 0],
            type: 'TEST'),
        NotificationModel(
            id: 2,
            recipientId: 123,
            message: 'Test 2',
            read: true,
            createdAt: [2024, 1, 15, 10, 30, 0, 0],
            type: 'TEST'),
        NotificationModel(
            id: 3,
            recipientId: 123,
            message: 'Test 3',
            read: false,
            createdAt: [2024, 1, 15, 10, 30, 0, 0],
            type: 'TEST'),
      ];

      authProvider.notifications.addAll(notifications);

      // Act & Assert
      expect(authProvider.unreadNotificationsCount, 2);
    });

    test('should handle real-world API response format correctly', () async {
      // Arrange - This simulates the actual API response format from the server
      final user =
          User(userId: 38, userRole: 'rider', email: '<EMAIL>');
      authProvider.setUserForTesting(user);

      // This is the exact format returned by the API as described in the issue
      final realWorldResponse = [
        {
          "id": 19,
          "recipientId": 38,
          "message":
              "A new delivery (Code: MLA-34-E42194) has been assigned to you...",
          "read": false,
          "createdAt": [2025, 5, 29, 10, 6, 19, 342604000],
          "type": "DELIVERY_ASSIGNED"
        }
      ];

      when(mockApiService.get(any, requiresAuth: true)).thenAnswer((_) async =>
          ApiResponse<dynamic>(success: true, data: realWorldResponse));

      // Act
      final result = await authProvider.fetchNotifications();

      // Assert
      expect(result, true,
          reason:
              'fetchNotifications should return true for successful API call');
      expect(authProvider.notifications.length, 1,
          reason: 'Should parse one notification');
      expect(authProvider.notifications.first.id, 19,
          reason: 'Should parse notification ID correctly');
      expect(authProvider.notifications.first.recipientId, 38,
          reason: 'Should parse recipient ID correctly');
      expect(authProvider.notifications.first.type, 'DELIVERY_ASSIGNED',
          reason: 'Should parse notification type correctly');
      expect(authProvider.notifications.first.read, false,
          reason: 'Should parse read status correctly');
      expect(authProvider.notificationsError, null,
          reason: 'Should not have any error');
    });
  });
}
