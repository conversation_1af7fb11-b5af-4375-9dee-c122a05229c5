import 'package:flutter_test/flutter_test.dart';
import 'package:mialamobile/models/delivery_summary.dart';
import 'package:mialamobile/api/endpoints.dart';

void main() {
  group('Delivery Summary Integration Tests', () {
    test('DeliverySummary model should parse JSON correctly', () {
      // Test data based on the expected API response
      final testData = {
        'totalDeliveries': 15,
        'deliveredCount': 12,
        'pendingCount': 3,
      };

      final deliverySummary = DeliverySummary.fromJson(testData);

      expect(deliverySummary.totalDeliveries, equals(15));
      expect(deliverySummary.deliveredCount, equals(12));
      expect(deliverySummary.pendingCount, equals(3));
    });

    test('DeliverySummary model should handle missing fields with defaults', () {
      // Test data with missing fields
      final testData = <String, dynamic>{};

      final deliverySummary = DeliverySummary.fromJson(testData);

      expect(deliverySummary.totalDeliveries, equals(0));
      expect(deliverySummary.deliveredCount, equals(0));
      expect(deliverySummary.pendingCount, equals(0));
    });

    test('DeliverySummary model should convert to JSON correctly', () {
      final deliverySummary = DeliverySummary(
        totalDeliveries: 20,
        deliveredCount: 18,
        pendingCount: 2,
      );

      final json = deliverySummary.toJson();

      expect(json['totalDeliveries'], equals(20));
      expect(json['deliveredCount'], equals(18));
      expect(json['pendingCount'], equals(2));
    });

    test('DeliverySummary toString should work correctly', () {
      final deliverySummary = DeliverySummary(
        totalDeliveries: 10,
        deliveredCount: 8,
        pendingCount: 2,
      );

      final stringRepresentation = deliverySummary.toString();

      expect(stringRepresentation, contains('totalDeliveries: 10'));
      expect(stringRepresentation, contains('deliveredCount: 8'));
      expect(stringRepresentation, contains('pendingCount: 2'));
    });

    test('API endpoint should be correctly defined', () {
      expect(ApiEndpoints.deliverySummary, isNotEmpty);
      expect(ApiEndpoints.deliverySummary, contains('/rider/delivery-summary'));
    });
  });
}
