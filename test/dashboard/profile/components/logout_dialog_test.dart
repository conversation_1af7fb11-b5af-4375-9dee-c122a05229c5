import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mialamobile/dashboard/profile/components/logout_dialog.dart';
import 'package:mialamobile/providers/auth_provider.dart';
import 'package:mockito/mockito.dart';
import 'package:provider/provider.dart';

class MockAuthProvider extends Mock implements AuthProvider {
  @override
  Future<Map<String, dynamic>> logout() async {
    return {
      'success': true,
      'message': 'Logged out successfully',
    };
  }
}

void main() {
  late MockAuthProvider mockAuthProvider;

  setUp(() {
    mockAuthProvider = MockAuthProvider();
  });

  testWidgets('LogoutDialog shows confirmation message and buttons', (WidgetTester tester) async {
    // Build the dialog
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: ChangeNotifierProvider<AuthProvider>.value(
            value: mockAuthProvider,
            child: Builder(
              builder: (context) => TextButton(
                onPressed: () => showLogoutDialog(context),
                child: const Text('Show Dialog'),
              ),
            ),
          ),
        ),
      ),
    );

    // Tap the button to show the dialog
    await tester.tap(find.text('Show Dialog'));
    await tester.pumpAndSettle();

    // Verify dialog content
    expect(find.text('Confirm Logout'), findsOneWidget);
    expect(find.text('Are you sure you want to logout?'), findsOneWidget);
    expect(find.text('Cancel'), findsOneWidget);
    expect(find.text('Logout'), findsOneWidget);
  });

  testWidgets('Cancel button dismisses the dialog', (WidgetTester tester) async {
    // Build the dialog
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: ChangeNotifierProvider<AuthProvider>.value(
            value: mockAuthProvider,
            child: Builder(
              builder: (context) => TextButton(
                onPressed: () => showLogoutDialog(context),
                child: const Text('Show Dialog'),
              ),
            ),
          ),
        ),
      ),
    );

    // Tap the button to show the dialog
    await tester.tap(find.text('Show Dialog'));
    await tester.pumpAndSettle();

    // Verify dialog is shown
    expect(find.text('Confirm Logout'), findsOneWidget);

    // Tap the Cancel button
    await tester.tap(find.text('Cancel'));
    await tester.pumpAndSettle();

    // Verify dialog is dismissed
    expect(find.text('Confirm Logout'), findsNothing);
  });

  // Note: Testing the actual logout functionality would require more complex mocking
  // of the navigation and API calls, which is beyond the scope of this simple test.
}
