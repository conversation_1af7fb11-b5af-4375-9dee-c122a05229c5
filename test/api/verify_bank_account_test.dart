import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:mialamobile/api/api_service.dart';
import 'package:mialamobile/api/endpoints.dart';
import 'package:mialamobile/providers/auth_provider.dart';

@GenerateMocks([http.Client, ApiService])
import 'verify_bank_account_test.mocks.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  group('Verify Bank Account API Tests', () {
    late MockApiService mockApiService;
    late AuthProvider authProvider;

    setUp(() {
      mockApiService = MockApiService();
      authProvider = AuthProvider();
      authProvider.apiService = mockApiService;
    });

    test('verifyBankAccount API call should send correct data format',
        () async {
      // Arrange
      final bankData = {
        'accountNumber': '**********',
        'bankName':
            '999992', // API expects 'bankName' but we're sending bank code
      };

      final successResponse = ApiResponse<Map<String, dynamic>>(
        success: true,
        data: {
          'responseCode': '00',
          'responseMsg': 'Success',
          'data': {
            'account_number': '**********',
            'account_name': 'EVELYN OGADIMMA OKAFOR'
          }
        },
      );

      when(mockApiService.get(
        ApiEndpoints.verifyBankAccount,
        queryParams: bankData,
        requiresAuth: false,
      )).thenAnswer((_) async => successResponse);

      // Act
      final result = await authProvider.verifyBankAccount(
        accountNumber: bankData['accountNumber']!,
        bankCode: bankData[
            'bankName']!, // Using bankCode parameter but passing the same value
      );

      // Assert
      verify(mockApiService.get(
        ApiEndpoints.verifyBankAccount,
        queryParams: bankData,
        requiresAuth: false,
      )).called(1);

      expect(result['success'], true);
      expect(result['accountName'], 'EVELYN OGADIMMA OKAFOR');
    });

    test('verifyBankAccount should handle error response correctly', () async {
      // Arrange
      final bankData = {
        'accountNumber': '**********',
        'bankName':
            '120001', // API expects 'bankName' but we're sending bank code
      };

      final errorResponse = ApiResponse<Map<String, dynamic>>(
        success: false,
        error: 'Error resolving account details',
        data: {
          'responseCode': '55',
          'responseMsg': 'Failed',
          'responseDesc': 'Error resolving account details'
        },
      );

      when(mockApiService.get(
        ApiEndpoints.verifyBankAccount,
        queryParams: bankData,
        requiresAuth: false,
      )).thenAnswer((_) async => errorResponse);

      // Act
      final result = await authProvider.verifyBankAccount(
        accountNumber: bankData['accountNumber']!,
        bankCode: bankData[
            'bankName']!, // Using bankCode parameter but passing the same value
      );

      // Assert
      verify(mockApiService.get(
        ApiEndpoints.verifyBankAccount,
        queryParams: bankData,
        requiresAuth: false,
      )).called(1);

      expect(result['success'], false);
      expect(result['message'], 'Error resolving account details');
    });
  });

  // We'll focus on the AuthProvider tests for now
}
