import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:mialamobile/api/api_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

@GenerateMocks([http.Client])
import 'api_service_test.mocks.dart';

void main() {
  // Initialize Flutter binding
  TestWidgetsFlutterBinding.ensureInitialized();

  // Set up mock for SharedPreferences
  SharedPreferences.setMockInitialValues({});
  late ApiService apiService;
  late MockClient mockClient;

  setUp(() async {
    mockClient = MockClient();
    apiService = ApiService();
    apiService.httpClient = mockClient;
    apiService.hasInternetConnectionOverride =
        true; // Assume internet is available
  });

  group('_processResponse', () {
    test('should handle successful response with responseCode 00', () async {
      // Arrange
      final responseBody = jsonEncode({
        'responseCode': '00',
        'responseMsg': 'Success',
        'responseDesc': 'Operation completed successfully',
        'data': {'id': 1, 'name': 'Test User'}
      });

      when(mockClient.post(
        any,
        headers: anyNamed('headers'),
        body: anyNamed('body'),
      )).thenAnswer((_) async => http.Response(responseBody, 200));

      // Act
      final result = await apiService
          .post<Map<String, dynamic>>('https://example.com/api');

      // Assert
      expect(result.success, true);
      expect(result.data?['responseCode'], '00');
      expect(result.data?['responseMsg'], 'Success');
      expect(result.error, null);
    });

    test(
        'should handle error response with responseCode 55 despite 200 HTTP status',
        () async {
      // Arrange
      final responseBody = jsonEncode({
        'responseCode': '55',
        'responseMsg': 'Failed',
        'responseDesc':
            'Validation failed for argument [0]: default message [Validation failed for email field]',
        'data': null
      });

      when(mockClient.post(
        any,
        headers: anyNamed('headers'),
        body: anyNamed('body'),
      )).thenAnswer((_) async => http.Response(responseBody, 200));

      // Act
      final result = await apiService
          .post<Map<String, dynamic>>('https://example.com/api');

      // Assert
      expect(result.success, false);
      expect(result.error, 'Validation failed for email field');
      expect(result.data, null);
    });

    test('should handle HTTP error response', () async {
      // Arrange
      final responseBody = jsonEncode({'message': 'Internal server error'});

      when(mockClient.post(
        any,
        headers: anyNamed('headers'),
        body: anyNamed('body'),
      )).thenAnswer((_) async => http.Response(responseBody, 500));

      // Act
      final result = await apiService
          .post<Map<String, dynamic>>('https://example.com/api');

      // Assert
      expect(result.success, false);
      expect(result.error, 'Internal server error');
      expect(result.data, null);
    });
  });
}
