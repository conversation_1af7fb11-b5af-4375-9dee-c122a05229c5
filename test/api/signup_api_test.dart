import 'dart:convert';

import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:mialamobile/api/api_service.dart';
import 'package:mialamobile/api/endpoints.dart';
import 'package:mialamobile/providers/auth_provider.dart';

@GenerateMocks([http.Client, ApiService])
import 'signup_api_test.mocks.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  group('Signup API Tests', () {
    late MockApiService mockApiService;
    late AuthProvider authProvider;

    setUp(() {
      mockApiService = MockApiService();
      authProvider = AuthProvider();
      authProvider.apiService = mockApiService;
    });

    test('signup API call should send correct data format', () async {
      // Arrange
      final signupData = {
        'first_name': '<PERSON>',
        'last_name': '<PERSON><PERSON>',
        'email': '<EMAIL>',
        'password': 'password123',
        'phone': '**********',
        'accountNumber': '**********',
        'accountName': 'John Doe',
        'bankName': 'Test Bank',
        'state': 'Test State',
      };

      final successResponse = ApiResponse<Map<String, dynamic>>(
        success: true,
        data: {
          'responseCode': '00',
          'responseMsg': 'Registration successful',
          'responseDesc': 'User registered successfully',
          'data': {'userId': 123, 'userRole': 'user'}
        },
      );

      when(mockApiService.post(
        ApiEndpoints.register,
        body: signupData,
        requiresAuth: false,
      )).thenAnswer((_) async => successResponse);

      // Act
      final result = await authProvider.signup(
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'password123',
        phone: '**********',
        accountNumber: '**********',
        accountName: 'John Doe',
        bankName: 'Test Bank',
        state: 'Test State',
      );

      // Assert
      verify(mockApiService.post(
        ApiEndpoints.register,
        body: signupData,
        requiresAuth: false,
      )).called(1);

      expect(result['success'], true);
      expect(result['message'], 'Registration successful');
    });

    test('signup API should handle error response correctly', () async {
      // Arrange
      final signupData = {
        'first_name': 'John',
        'last_name': 'Doe',
        'email': '<EMAIL>',
        'password': 'password123',
        'phone': '**********',
        'accountNumber': '**********',
        'accountName': 'John Doe',
        'bankName': 'Test Bank',
        'state': 'Test State',
      };

      final errorResponse = ApiResponse<Map<String, dynamic>>(
        success: false,
        error: 'Email already exists',
      );

      when(mockApiService.post(
        ApiEndpoints.register,
        body: signupData,
        requiresAuth: false,
      )).thenAnswer((_) async => errorResponse);

      // Act
      final result = await authProvider.signup(
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'password123',
        phone: '**********',
        accountNumber: '**********',
        accountName: 'John Doe',
        bankName: 'Test Bank',
        state: 'Test State',
      );

      // Assert
      verify(mockApiService.post(
        ApiEndpoints.register,
        body: signupData,
        requiresAuth: false,
      )).called(1);

      expect(result['success'], false);
      expect(result['message'], 'Email already exists');
    });

    test('signup API should handle network errors', () async {
      // Arrange
      final signupData = {
        'first_name': 'John',
        'last_name': 'Doe',
        'email': '<EMAIL>',
        'password': 'password123',
        'phone': '**********',
        'accountNumber': '**********',
        'accountName': 'John Doe',
        'bankName': 'Test Bank',
        'state': 'Test State',
      };

      when(mockApiService.post(
        ApiEndpoints.register,
        body: signupData,
        requiresAuth: false,
      )).thenThrow(Exception('Network error'));

      // Act
      final result = await authProvider.signup(
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'password123',
        phone: '**********',
        accountNumber: '**********',
        accountName: 'John Doe',
        bankName: 'Test Bank',
        state: 'Test State',
      );

      // Assert
      verify(mockApiService.post(
        ApiEndpoints.register,
        body: signupData,
        requiresAuth: false,
      )).called(1);

      expect(result['success'], false);
      expect(
          result['message'], 'An unexpected error occurred. Please try again.');
    });
  });

  group('ApiService POST Method Tests', () {
    late MockClient mockClient;
    late ApiService apiService;

    setUp(() {
      mockClient = MockClient();
      apiService = ApiService();
      // Inject the mock HTTP client
      apiService.httpClient = mockClient;

      // Mock the hasInternetConnection method to always return true
      apiService.hasInternetConnectionOverride = true;
    });

    test('POST request should format signup data correctly', () async {
      // Arrange
      final signupData = {
        'first_name': 'John',
        'last_name': 'Doe',
        'email': '<EMAIL>',
        'password': 'password123',
        'phone': '**********',
        'accountNumber': '**********',
        'accountName': 'John Doe',
        'bankName': 'Test Bank',
        'state': 'Test State',
      };

      final successResponseJson = {
        'responseCode': '00',
        'responseMsg': 'Registration successful',
        'responseDesc': 'User registered successfully',
        'data': {'userId': 123, 'userRole': 'user'}
      };

      // Mock the HTTP response
      when(mockClient.post(
        Uri.parse(ApiEndpoints.register),
        headers: anyNamed('headers'),
        body: jsonEncode(signupData),
      )).thenAnswer((_) async => http.Response(
            jsonEncode(successResponseJson),
            200,
          ));

      // Act
      final response = await apiService.post(
        ApiEndpoints.register,
        body: signupData,
        requiresAuth: false,
      );

      // Assert
      verify(mockClient.post(
        Uri.parse(ApiEndpoints.register),
        headers: anyNamed('headers'),
        body: jsonEncode(signupData),
      )).called(1);

      expect(response.success, true);
      expect(response.data, successResponseJson);
    });

    test('POST request should handle error response correctly', () async {
      // Arrange
      final signupData = {
        'first_name': 'John',
        'last_name': 'Doe',
        'email': '<EMAIL>',
        'password': 'password123',
        'phone': '**********',
        'accountNumber': '**********',
        'accountName': 'John Doe',
        'bankName': 'Test Bank',
        'state': 'Test State',
      };

      final errorResponseJson = {
        'responseCode': '400',
        'responseMsg': 'Email already exists',
        'responseDesc': 'The email address is already registered',
        'message': 'Email already exists'
      };

      // Mock the HTTP response
      when(mockClient.post(
        Uri.parse(ApiEndpoints.register),
        headers: anyNamed('headers'),
        body: jsonEncode(signupData),
      )).thenAnswer((_) async => http.Response(
            jsonEncode(errorResponseJson),
            400,
          ));

      // Act
      final response = await apiService.post(
        ApiEndpoints.register,
        body: signupData,
        requiresAuth: false,
      );

      // Assert
      verify(mockClient.post(
        Uri.parse(ApiEndpoints.register),
        headers: anyNamed('headers'),
        body: jsonEncode(signupData),
      )).called(1);

      expect(response.success, false);
      expect(response.error, 'Email already exists');
    });
  });
}
