import 'dart:convert';
import 'dart:async';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'api_config.dart';
import 'endpoints.dart';
import 'dart:developer' as developer;
import 'package:connectivity_plus/connectivity_plus.dart';

class ApiResponse<T> {
  final T? data;
  final String? error;
  final bool success;

  ApiResponse({this.data, this.error, required this.success});
}

class ApiService {
  // HTTP client that can be injected for testing
  http.Client _client = http.Client();

  // Setter for injecting mock HTTP client in tests
  set httpClient(http.Client client) {
    _client = client;
  }

  // For testing purposes - override internet connection check
  bool? _hasInternetConnectionOverride;
  set hasInternetConnectionOverride(bool value) {
    _hasInternetConnectionOverride = value;
  }

  // Get the auth token from shared preferences
  Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token');

    // Check if token is expired
    if (token != null && await isTokenExpired()) {
      developer.log('Token is expired, returning null', name: 'api_service');
      return null;
    }

    return token;
  }

  // Save the auth token to shared preferences
  Future<void> saveToken(String? token, {int expiresInDays = 30}) async {
    if (token == null) return;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('auth_token', token);

    // Save token expiration time
    final expirationTime = DateTime.now()
        .add(Duration(days: expiresInDays))
        .millisecondsSinceEpoch;
    await prefs.setInt('token_expiration', expirationTime);
    developer.log(
        'Token saved with expiration: ${DateTime.fromMillisecondsSinceEpoch(expirationTime)}',
        name: 'api_service');
  }

  // Check if the token is expired
  Future<bool> isTokenExpired() async {
    final prefs = await SharedPreferences.getInstance();
    final expirationTime = prefs.getInt('token_expiration');

    if (expirationTime == null) {
      // If no expiration time is set, consider the token expired
      return true;
    }

    final now = DateTime.now().millisecondsSinceEpoch;
    final isExpired = now > expirationTime;

    if (isExpired) {
      developer.log(
          'Token is expired. Expiration: ${DateTime.fromMillisecondsSinceEpoch(expirationTime)}, Now: ${DateTime.now()}',
          name: 'api_service');
    }

    return isExpired;
  }

  // Clear the auth token from shared preferences
  Future<void> clearToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
    await prefs.remove('token_expiration');
    developer.log('Token and expiration cleared', name: 'api_service');
  }

  // Save user data to shared preferences
  Future<void> saveUserData(Map<String, dynamic> userData) async {
    if (userData.isEmpty) return;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_data', jsonEncode(userData));
    developer.log('User data saved to preferences', name: 'api_service');
  }

  // Get user data from shared preferences
  Future<Map<String, dynamic>?> getUserData() async {
    final prefs = await SharedPreferences.getInstance();
    final userDataString = prefs.getString('user_data');

    if (userDataString == null) return null;

    try {
      return jsonDecode(userDataString) as Map<String, dynamic>;
    } catch (e) {
      developer.log('Error decoding user data: $e', name: 'api_service');
      return null;
    }
  }

  // Clear user data from shared preferences
  Future<void> clearUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('user_data');
    developer.log('User data cleared', name: 'api_service');
  }

  Future<bool> hasInternetConnection() async {
    // If override is set, use that value instead of checking connectivity
    if (_hasInternetConnectionOverride != null) {
      return _hasInternetConnectionOverride!;
    }

    try {
      final result = await Connectivity().checkConnectivity();
      developer.log('Connectivity result: $result', name: 'api_service');

      // For now, assume we have internet connection to avoid issues with the connectivity package
      // This will allow us to test the actual API call
      return true;
    } catch (e) {
      developer.log('Error checking connectivity: $e', name: 'api_service');
      // Assume we have internet connection if there's an error checking
      return true;
    }
  }

  // Generic POST request
  Future<ApiResponse<T>> post<T>(String url,
      {Map<String, dynamic>? body,
      bool requiresAuth = true,
      int retries = 2}) async {
    developer.log('Making POST request to: $url', name: 'api_service');
    developer.log('Request body: $body', name: 'api_service');
    developer.log('Requires auth: $requiresAuth', name: 'api_service');

    // Check for internet connectivity first
    final hasConnection = await hasInternetConnection();
    developer.log('Internet connection check result: $hasConnection',
        name: 'api_service');
    if (!hasConnection) {
      developer.log('No internet connection available', name: 'api_service');
      return ApiResponse(
        success: false,
        error:
            'No internet connection. Please check your network settings and try again.',
      );
    }

    int attempts = 0;

    while (attempts <= retries) {
      attempts++;
      developer.log('POST attempt $attempts of ${retries + 1}',
          name: 'api_service');

      try {
        final uri = Uri.parse(url);
        final headers =
            ApiConfig.getHeaders(token: requiresAuth ? await getToken() : null);
        final encodedBody = jsonEncode(body);

        developer.log('Request URI: $uri', name: 'api_service');
        developer.log('Request headers: $headers', name: 'api_service');
        developer.log('Encoded body: $encodedBody', name: 'api_service');

        final response = await _client
            .post(uri, headers: headers, body: encodedBody)
            .timeout(ApiConfig.timeout);

        developer.log('Response status code: ${response.statusCode}',
            name: 'api_service');
        developer.log('Response body: ${response.body}', name: 'api_service');

        return _processResponse<T>(response);
      } on TimeoutException catch (e) {
        developer.log('TimeoutException: $e', name: 'api_service');

        if (attempts > retries) {
          developer.log('Maximum retry attempts reached', name: 'api_service');
          return ApiResponse(
            success: false,
            error:
                'Network timeout: The server is taking too long to respond. Please try again later.',
          );
        }

        // Wait before retrying (exponential backoff)
        final waitTime = Duration(seconds: attempts * 2);
        developer.log('Waiting ${waitTime.inSeconds} seconds before retry...',
            name: 'api_service');
        await Future.delayed(waitTime);
        continue; // Retry the request
      } catch (e) {
        developer.log('Error during POST request: $e', name: 'api_service');
        return ApiResponse(
          success: false,
          error: 'Network error: ${e.toString()}',
        );
      }
    }

    // This should never be reached, but just in case
    return ApiResponse(
      success: false,
      error: 'Unknown error occurred',
    );
  }

  // Generic GET request with query parameters
  Future<ApiResponse<T>> get<T>(String url,
      {Map<String, dynamic>? queryParams,
      bool requiresAuth = true,
      int retries = 2,
      bool useExtendedTimeout = false}) async {
    developer.log('Making GET request to: $url', name: 'api_service');
    developer.log('Query parameters: $queryParams', name: 'api_service');
    developer.log('Requires auth: $requiresAuth', name: 'api_service');
    developer.log('Using extended timeout: $useExtendedTimeout',
        name: 'api_service');

    // Check for internet connectivity first
    final hasConnection = await hasInternetConnection();
    developer.log('Internet connection check result: $hasConnection',
        name: 'api_service');
    if (!hasConnection) {
      developer.log('No internet connection available', name: 'api_service');
      return ApiResponse(
        success: false,
        error:
            'No internet connection. Please check your network settings and try again.',
      );
    }

    int attempts = 0;

    while (attempts <= retries) {
      attempts++;
      developer.log('GET attempt $attempts of ${retries + 1}',
          name: 'api_service');

      try {
        // Build URI with query parameters
        final uri = Uri.parse(url).replace(
          queryParameters:
              queryParams?.map((key, value) => MapEntry(key, value.toString())),
        );

        final headers =
            ApiConfig.getHeaders(token: requiresAuth ? await getToken() : null);

        developer.log('Request URI: $uri', name: 'api_service');
        developer.log('Request headers: $headers', name: 'api_service');

        // Use extended timeout for specific endpoints if requested
        final timeout =
            useExtendedTimeout ? ApiConfig.extendedTimeout : ApiConfig.timeout;
        developer.log('Using timeout: ${timeout.inSeconds} seconds',
            name: 'api_service');

        final response =
            await _client.get(uri, headers: headers).timeout(timeout);

        developer.log('Response status code: ${response.statusCode}',
            name: 'api_service');
        developer.log('Response body: ${response.body}', name: 'api_service');

        return _processResponse<T>(response);
      } on TimeoutException catch (e) {
        developer.log('TimeoutException: $e', name: 'api_service');

        if (attempts > retries) {
          developer.log('Maximum retry attempts reached', name: 'api_service');
          return ApiResponse(
            success: false,
            error:
                'Network timeout: The server is taking too long to respond. Please try again later.',
          );
        }

        // Wait before retrying (exponential backoff)
        final waitTime = Duration(seconds: attempts * 2);
        developer.log('Waiting ${waitTime.inSeconds} seconds before retry...',
            name: 'api_service');
        await Future.delayed(waitTime);
        continue; // Retry the request
      } catch (e) {
        developer.log('Error during GET request: $e', name: 'api_service');
        return ApiResponse(
          success: false,
          error: 'Network error: ${e.toString()}',
        );
      }
    }

    // This should never be reached, but just in case
    return ApiResponse(
      success: false,
      error: 'Unknown error occurred',
    );
  }

  // Direct API test for banks list
  Future<Map<String, dynamic>> testBanksListApi() async {
    developer.log('Testing banks list API directly', name: 'api_service');

    try {
      // Create a direct HTTP client
      final client = http.Client();

      // Make a direct GET request to the banks list API
      final uri = Uri.parse(ApiEndpoints.bank);
      developer.log('Direct API test URI: $uri', name: 'api_service');

      // Add headers
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };
      developer.log('Direct API test headers: $headers', name: 'api_service');

      // Make the request with a longer timeout
      final response = await client
          .get(uri, headers: headers)
          .timeout(const Duration(seconds: 60));

      developer.log('Direct API test status code: ${response.statusCode}',
          name: 'api_service');
      developer.log('Direct API test response body: ${response.body}',
          name: 'api_service');

      // Parse the response
      final jsonData = jsonDecode(response.body);

      // Close the client
      client.close();

      return {
        'success': response.statusCode >= 200 && response.statusCode < 300,
        'statusCode': response.statusCode,
        'data': jsonData,
      };
    } catch (e) {
      developer.log('Direct API test error: $e', name: 'api_service');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  // Process the HTTP response
  ApiResponse<T> _processResponse<T>(http.Response response) {
    try {
      // Log the raw response for debugging
      developer.log('Raw response body: ${response.body}', name: 'api_service');

      final jsonData = jsonDecode(response.body);
      developer.log('Decoded response: $jsonData', name: 'api_service');

      // First check HTTP status code
      if (response.statusCode >= 200 && response.statusCode < 300) {
        // Then check for API-specific response codes if they exist
        if (jsonData is Map &&
            jsonData.containsKey('responseCode') &&
            jsonData.containsKey('responseMsg')) {
          // API returns responseCode and responseMsg fields
          final responseCode = jsonData['responseCode'];
          final responseMsg = jsonData['responseMsg'];

          // Check if the API response indicates success
          // Assuming "00" is success code based on common API patterns
          if (responseCode == "00" || responseMsg?.toLowerCase() == "success") {
            developer.log(
                'API request successful: $responseCode - $responseMsg',
                name: 'api_service');
            return ApiResponse<T>(success: true, data: jsonData as T);
          } else {
            // Extract detailed error message from responseDesc if available
            String errorMessage = '';

            // Check if responseDesc contains validation error message
            if (jsonData['responseDesc'] != null &&
                jsonData['responseDesc']
                    .toString()
                    .contains('Validation failed')) {
              // Extract the specific validation error message
              final fullErrorMsg = jsonData['responseDesc'].toString();

              // Try to extract the specific validation error message
              final validationMsgMatch =
                  RegExp(r'default message \[(.*?)\]').firstMatch(fullErrorMsg);
              if (validationMsgMatch != null &&
                  validationMsgMatch.groupCount >= 1) {
                errorMessage = validationMsgMatch.group(1) ?? '';
              } else {
                // If we can't extract the specific message, use a more user-friendly version
                errorMessage =
                    'Validation failed. Please check your input and try again.';
              }
            } else {
              // Use the responseDesc or a fallback message
              errorMessage = jsonData['responseDesc'] ??
                  'Error: ${jsonData['responseMsg']} (Code: ${jsonData['responseCode']})';
            }

            developer.log('API request failed: $errorMessage',
                name: 'api_service');
            return ApiResponse<T>(success: false, error: errorMessage);
          }
        } else {
          // Response doesn't have the expected fields, treat as success
          developer.log(
              'Request successful (no responseCode/responseMsg fields)',
              name: 'api_service');
          return ApiResponse<T>(success: true, data: jsonData as T);
        }
      } else {
        // HTTP error
        String errorMessage = '';
        if (jsonData is Map) {
          errorMessage = jsonData['message'] ??
              jsonData['responseDesc'] ??
              'Unknown error occurred (HTTP ${response.statusCode})';
        } else {
          errorMessage = 'Unknown error occurred (HTTP ${response.statusCode})';
        }

        developer.log('HTTP request failed: $errorMessage',
            name: 'api_service');
        return ApiResponse<T>(success: false, error: errorMessage);
      }
    } catch (e) {
      developer.log('Error processing response: $e', name: 'api_service');
      developer.log('Raw response that caused error: ${response.body}',
          name: 'api_service');
      return ApiResponse<T>(
          success: false, error: 'Error processing response: ${e.toString()}');
    }
  }
}
