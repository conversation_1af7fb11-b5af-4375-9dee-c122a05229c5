class ApiConfig {
  // Base URL for your API
  static const String baseUrl = 'https://miala.onrender.com';

  // API version path component
  static const String apiVersion = 'v1';

  // Full API path with version
  static const String apiPath = '$baseUrl/api/$apiVersion';

  // Timeout duration for API calls
  static const Duration timeout = Duration(seconds: 30);

  // Extended timeout for slow endpoints like banks list
  static const Duration extendedTimeout = Duration(seconds: 60);

  // Headers for API requests
  static Map<String, String> getHeaders({String? token}) {
    Map<String, String> headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }
}
