import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mialamobile/components/primary_button.dart';
import 'package:mialamobile/providers/auth_provider.dart';
import 'package:provider/provider.dart';

class LogoutDialog extends StatefulWidget {
  const LogoutDialog({super.key});

  @override
  State<LogoutDialog> createState() => _LogoutDialogState();
}

class _LogoutDialogState extends State<LogoutDialog> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.symmetric(horizontal: 24),
      child: Container(
        width: 300,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: const Color(0xff1E1E1E),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.with<PERSON><PERSON><PERSON>(50),
              blurRadius: 10,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Confirm Logout',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: const Color(0xffB10303),
              ),
            ),
            const Gap(16),
            Text(
              'Are you sure you want to logout?',
              textAlign: TextAlign.center,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.white,
              ),
            ),
            const Gap(28),
            // Buttons
            _isLoading
                ? const CircularProgressIndicator(
                    color: Color(0xffB10303),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Cancel button
                      Expanded(
                        child: PrimaryButton(
                          text: 'Cancel',
                          onPressed: () => Navigator.of(context).pop(),
                          height: 44,
                          backgroundColor: const Color(0xff191818),
                          borderRadius: 8,
                          fontWeight: FontWeight.w600,
                          textSize: 14,
                          borderColor: const Color(0xff8C8C8C),
                        ),
                      ),
                      // Spacing between buttons
                      const SizedBox(width: 16),
                      // Logout button
                      Expanded(
                        child: PrimaryButton(
                          text: 'Logout',
                          onPressed: () => _handleLogout(context),
                          height: 44,
                          backgroundColor: const Color(0xffB10303),
                          borderRadius: 8,
                          fontWeight: FontWeight.w600,
                          textSize: 14,
                        ),
                      ),
                    ],
                  ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleLogout(BuildContext context) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await authProvider.logout();

      // Check if widget is still mounted before proceeding
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      if (context.mounted) {
        // Close the dialog
        Navigator.of(context).pop();

        // Show a snackbar with the result message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result['message'] ?? 'Logged out'),
            backgroundColor:
                result['success'] == true ? Colors.green : Colors.red,
          ),
        );

        // Navigate to login page and clear navigation stack
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/onboarding',
          (route) => false, // This prevents going back
        );
      }
    } catch (e) {
      // Handle any unexpected errors
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (context.mounted) {
          // Close the dialog
          Navigator.of(context).pop();

          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error during logout: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}

// Helper function to show the dialog
void showLogoutDialog(BuildContext context) {
  showDialog(
    context: context,
    barrierDismissible: false, // Prevent dismissing by tapping outside
    barrierColor: const Color(
        0xB3000000), // Darker barrier for better contrast (70% opacity)
    useSafeArea: true, // Ensure dialog respects safe area
    builder: (BuildContext context) => const LogoutDialog(),
  );
}
