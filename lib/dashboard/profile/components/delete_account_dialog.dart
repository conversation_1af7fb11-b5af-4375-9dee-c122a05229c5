import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mialamobile/components/primary_button.dart';

class DeleteAccountDialog extends StatelessWidget {
  const DeleteAccountDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Dialog content
          Container(
            width: 267,
            height: 164,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: const Color(0xff1E1E1E),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Text(
                  'Delete Account',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xffB10303),
                  ),
                ),
                const Gap(10),
                Text(
                  'Are you sure you want to delete your account?',
                  textAlign: TextAlign.center,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: const Color(0xff8C8C8C),
                  ),
                ),
                const Gap(10),
                // Cancel button
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    PrimaryButton(
                        text: 'Cancel',
                        onPressed: () => Navigator.of(context).pop(),
                        width: 97,
                        height: 34,
                        backgroundColor: const Color(0xff191818),
                        borderRadius: 5,
                        fontWeight: FontWeight.w600,
                        textSize: 12),
                    const Gap(5),
                    PrimaryButton(
                        text: 'Delete',
                        onPressed: () => Navigator.of(context).pop(),
                        width: 97,
                        height: 34,
                        backgroundColor: const Color(0xffB10303),
                        borderRadius: 5,
                        fontWeight: FontWeight.w600,
                        textSize: 12),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// Helper function to show the dialog
void showDeleteAccountDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (BuildContext context) => const DeleteAccountDialog(),
  );
}
