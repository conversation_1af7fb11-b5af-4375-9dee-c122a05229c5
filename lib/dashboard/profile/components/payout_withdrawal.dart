import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mialamobile/components/input_type.dart';
import 'package:mialamobile/components/primary_button.dart';
import 'package:mialamobile/dashboard/dashboard_page.dart';

class PayoutWithdrawal extends StatefulWidget {
  const PayoutWithdrawal({super.key});

  @override
  State<PayoutWithdrawal> createState() => _PayoutWithdrawalState();
}

class _PayoutWithdrawalState extends State<PayoutWithdrawal> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        iconTheme: const IconThemeData(color: Color(0xff8C8C8C)),
      ),
      backgroundColor: const Color(0xff121212),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 32),
            child: Column(mainAxisSize: MainAxisSize.min, children: [
              Text(
                'Payouts Withdrawal',
                style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w500,
                    fontSize: 24,
                    color: Colors.white),
              ),
              const Gap(29),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Current Balance',
                    style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w400,
                        fontSize: 10,
                        color: const Color(0xffC8C8C8)),
                  ),
                  const Gap(5),
                  SvgPicture.asset('assets/icons/view.svg')
                ],
              ),
              const Gap(5),
              Text(
                '₦70,000.00',
                style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    fontSize: 32,
                    color: Colors.white),
              ),
              const Gap(22),
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                Text(
                  'Account Number',
                  style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w300,
                      fontSize: 10,
                      color: const Color(0xffC8C8C8)),
                ),
                Text(
                  '*********',
                  style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                      color: Colors.white),
                ),
              ]),
              const Gap(16),
              const Divider(
                color: Color(0xffC8C8C8),
                thickness: 0.5,
              ),
              const Gap(22),
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                Text(
                  'Bank ',
                  style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w300,
                      fontSize: 10,
                      color: const Color(0xffC8C8C8)),
                ),
                Text(
                  'United Bank Of Africa',
                  style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                      color: Colors.white),
                ),
              ]),
              const Gap(16),
              const Divider(
                color: Color(0xffC8C8C8),
                thickness: 0.5,
              ),
              const Gap(22),
              InputType(
                labelText: 'Amount',
                hintText: 'Enter withdrawal amount',
                controller: TextEditingController(),
              ),
              const Gap(24),
              PrimaryButton(
                text: 'Done',
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        backgroundColor: const Color(0xff121212),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        content: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Image.asset('assets/images/success.png'),
                            const Gap(10),
                            Text(
                              'Success!',
                              style: GoogleFonts.poppins(
                                fontWeight: FontWeight.w600,
                                fontSize: 24,
                                color: const Color(0xffB10303),
                              ),
                            ),
                            const Gap(4),
                            Text(
                              'You have successfully created an account',
                              textAlign: TextAlign.center,
                              style: GoogleFonts.poppins(
                                fontWeight: FontWeight.w400,
                                fontSize: 12,
                                color: const Color(0xffC8C8C8),
                              ),
                            ),
                            const Gap(24),
                            PrimaryButton(
                              text: 'OK',
                              onPressed: () {
                                Navigator.of(context).pop(); // Close the dialog
                                Navigator.pushReplacement(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          const DashboardPage()),
                                );
                              },
                              width: double.infinity,
                              backgroundColor: const Color(0xffB10303),
                            ),
                          ],
                        ),
                      );
                    },
                  );
                },
                width: MediaQuery.of(context).size.width,
                backgroundColor: const Color(0xffFF0000),
              ),
            ]),
          ),
        ),
      ),
    );
  }
}
