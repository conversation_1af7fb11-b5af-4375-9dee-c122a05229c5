import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';

class FingerprintDialog extends StatelessWidget {
  const FingerprintDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Dialog content
          Container(
            width: 267,
            height: 164,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: const Color(0xff1E1E1E),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Text(
                  'Finger Print Login',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                const Gap(10),
                Text(
                  'Put your fingerprint sensor or scan your face to authorize your account',
                  textAlign: TextAlign.center,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: const Color(0xff8C8C8C),
                  ),
                ),
                const Gap(10),
                // Cancel button
                SizedBox(
                  width: 160,
                  height: 44,
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: Color(0xff8C8C8C)),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: Center(
                      child: Text(
                        'Cancel',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xff8C8C8C),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Fingerprint icon
          const Gap(55),
          SvgPicture.asset('assets/icons/big_fingerprint.svg'),
        ],
      ),
    );
  }
}

// Helper function to show the dialog
void showFingerprintDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (BuildContext context) => const FingerprintDialog(),
  );
}
