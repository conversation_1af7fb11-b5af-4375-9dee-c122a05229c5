import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mialamobile/dashboard/home/<USER>';
import 'package:mialamobile/dashboard/profile/components/delete_account_dialog.dart';
import 'package:mialamobile/dashboard/profile/components/fingerprint_dialog.dart';
import 'package:mialamobile/dashboard/profile/components/logout_dialog.dart';
import 'package:mialamobile/dashboard/profile/components/payout_account.dart';
import 'package:mialamobile/dashboard/profile/components/payout_withdrawal.dart';
import 'package:mialamobile/dashboard/profile/components/reset_password.dart';

class ProfileTab extends StatefulWidget {
  const ProfileTab({super.key});

  @override
  State<ProfileTab> createState() => _ProfileTabState();
}

class _ProfileTabState extends State<ProfileTab> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xff121212),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Profile',
                  style: GoogleFonts.poppins(
                    fontSize: 24,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
                const Gap(14),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Image.asset('assets/images/profile2.png'),
                        const Gap(10),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('John Okoro',
                                style: GoogleFonts.poppins(
                                    textStyle: const TextStyle(
                                        color: Color(0xffFFFFFF),
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500))),
                            Text('johnokoro@@gmail.com',
                                style: GoogleFonts.poppins(
                                    textStyle: const TextStyle(
                                        color: Color(0xffD9D9D9),
                                        fontSize: 10,
                                        fontWeight: FontWeight.w500)))
                          ],
                        )
                      ],
                    ),
                    InkWell(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const NotificationPage(),
                            ),
                          );
                        },
                        child: SvgPicture.asset('assets/icons/edit.svg')),
                  ],
                ),
                const Gap(42),
                Text('Settings',
                    style: GoogleFonts.poppins(
                        textStyle: const TextStyle(
                            color: Color(0xff8e8e8e),
                            fontSize: 16,
                            fontWeight: FontWeight.w500))),
                const Gap(19),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        SvgPicture.asset('assets/icons/yellow_bell.svg'),
                        const Gap(13),
                        Text('Notification',
                            style: GoogleFonts.poppins(
                                textStyle: const TextStyle(
                                    color: Color(0xffFFFFFF),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500))),
                      ],
                    ),
                    SvgPicture.asset('assets/icons/switch.svg'),
                  ],
                ),
                const Gap(20),
                const Divider(color: Color(0xff8e8e8e), thickness: 0.5),
                const Gap(20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        SvgPicture.asset('assets/icons/fingerprint.svg'),
                        const Gap(13),
                        Text('Fingerprint',
                            style: GoogleFonts.poppins(
                                textStyle: const TextStyle(
                                    color: Color(0xffFFFFFF),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500))),
                      ],
                    ),
                    GestureDetector(
                      onTap: () => showFingerprintDialog(context),
                      child: SvgPicture.asset('assets/icons/switch.svg'),
                    ),
                  ],
                ),
                const Gap(20),
                const Divider(color: Color(0xff8e8e8e), thickness: 0.5),
                const Gap(20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        SvgPicture.asset('assets/icons/support.svg'),
                        const Gap(13),
                        Text('Support',
                            style: GoogleFonts.poppins(
                                textStyle: const TextStyle(
                                    color: Color(0xffFFFFFF),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500))),
                      ],
                    ),
                    SvgPicture.asset('assets/icons/arrow_right.svg'),
                  ],
                ),
                const Gap(20),
                const Divider(color: Color(0xff8e8e8e), thickness: 0.5),
                const Gap(20),
                InkWell(
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const PayoutAccount(),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          SvgPicture.asset('assets/icons/payout.svg'),
                          const Gap(13),
                          Text('Payout Account',
                              style: GoogleFonts.poppins(
                                  textStyle: const TextStyle(
                                      color: Color(0xffFFFFFF),
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500))),
                        ],
                      ),
                      SvgPicture.asset('assets/icons/arrow_right.svg'),
                    ],
                  ),
                ),
                const Gap(20),
                const Divider(color: Color(0xff8e8e8e), thickness: 0.5),
                const Gap(20),
                InkWell(
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const PayoutWithdrawal(),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          SvgPicture.asset('assets/icons/payout.svg'),
                          const Gap(13),
                          Text('Payout Withdrawal',
                              style: GoogleFonts.poppins(
                                  textStyle: const TextStyle(
                                      color: Color(0xffFFFFFF),
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500))),
                        ],
                      ),
                      SvgPicture.asset('assets/icons/arrow_right.svg'),
                    ],
                  ),
                ),
                const Gap(20),
                const Divider(color: Color(0xff8e8e8e), thickness: 0.5),
                const Gap(20),
                InkWell(
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const ResetPassword(),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          SvgPicture.asset('assets/icons/reset.svg'),
                          const Gap(13),
                          Text('Reset Password',
                              style: GoogleFonts.poppins(
                                  textStyle: const TextStyle(
                                      color: Color(0xffFFFFFF),
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500))),
                        ],
                      ),
                      SvgPicture.asset('assets/icons/arrow_right.svg'),
                    ],
                  ),
                ),
                const Gap(20),
                const Divider(color: Color(0xff8e8e8e), thickness: 0.5),
                const Gap(20),
                InkWell(
                  onTap: () => showDeleteAccountDialog(context),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          SvgPicture.asset('assets/icons/delete.svg'),
                          const Gap(13),
                          Text('Delete Account',
                              style: GoogleFonts.poppins(
                                  textStyle: const TextStyle(
                                      color: Color(0xffFFFFFF),
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500))),
                        ],
                      ),
                      SvgPicture.asset('assets/icons/arrow_right.svg'),
                    ],
                  ),
                ),
                const Gap(20),
                const Divider(color: Color(0xff8e8e8e), thickness: 0.5),
                const Gap(20),
                InkWell(
                  onTap: () => showLogoutDialog(context),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          const Icon(
                            Icons.logout,
                            color: Color(0xffB10303),
                            size: 20,
                          ),
                          const Gap(13),
                          Text('Logout',
                              style: GoogleFonts.poppins(
                                  textStyle: const TextStyle(
                                      color: Color(0xffFFFFFF),
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500))),
                        ],
                      ),
                      SvgPicture.asset('assets/icons/arrow_right.svg'),
                    ],
                  ),
                ),
                const Gap(20),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
