import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';

class NotificationBox extends StatelessWidget {
  final String title;
  final String subtitle;
  final String time;
  final String iconPath;
  final bool isRead;
  final VoidCallback? onTap;

  const NotificationBox({
    super.key,
    required this.title,
    required this.subtitle,
    required this.time,
    required this.iconPath,
    this.isRead = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: isRead ? const Color(0xff1E1E1E) : const Color(0xff2A2A2A),
          borderRadius: BorderRadius.circular(12),
          border: isRead
              ? null
              : Border.all(
                  color: const Color(0xffB10303).withValues(alpha: 0.3),
                  width: 1,
                ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Left side - Icon with unread indicator
            Stack(
              children: [
                SvgPicture.asset(iconPath),
                if (!isRead)
                  Positioned(
                    top: 0,
                    right: 0,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        color: Color(0xffB10303),
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
              ],
            ),
            const Gap(12),

            // Middle - Title and Subtitle
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const Gap(4),
                  Text(
                    subtitle,
                    style: GoogleFonts.poppins(
                      fontSize: 10,
                      fontWeight: FontWeight.w300,
                      color: const Color(0xff8C8C8C),
                    ),
                    maxLines: 2,
                    // overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

            // Right side - Time
            Padding(
              padding: const EdgeInsets.only(left: 8),
              child: Text(
                time,
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: const Color(0xff8C8C8C),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
