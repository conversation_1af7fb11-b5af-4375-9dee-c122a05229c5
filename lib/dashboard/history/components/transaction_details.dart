// File: lib/dashboard/history/components/transaction_details_dialog.dart
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';

class TransactionDetailsDialog extends StatelessWidget {
  final String orderId;
  final String date;
  final String time;
  final String packageName;
  final int quantity;
  final String receiverName;
  final String paymentMethod;
  final String status;
  final double totalAmount;
  final double deliveryFee;
  final double netAmount;

  const TransactionDetailsDialog({
    super.key,
    required this.orderId,
    required this.date,
    required this.time,
    required this.packageName,
    required this.quantity,
    required this.receiverName,
    required this.paymentMethod,
    required this.status,
    required this.totalAmount,
    required this.deliveryFee,
    required this.netAmount,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.symmetric(horizontal: 20),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: const Color(0xff1E1E1E),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Text(
                    'Transaction Details',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                  const Gap(5),
                  Text(
                    '$date, $time',
                    style: GoogleFonts.poppins(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xff8C8C8C),
                    ),
                  ),
                ],
              ),
            ),
            const Gap(16),
            // Details
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                children: [
                  _buildDetailRow('Package ID', orderId),
                  _buildDetailRow('Package', packageName),
                  _buildDetailRow('Amount', quantity.toString()),
                  _buildDetailRow('Receiver\'s Name', receiverName),
                  _buildDetailRow('Payment Method', paymentMethod),
                  _buildDetailRow('Status', status, isStatus: true),
                  _buildDetailRow('Total Amount Paid by Customer',
                      '₦${totalAmount.toStringAsFixed(0)}'),
                  _buildDetailRow('Delivery Fee Deducted',
                      '₦${deliveryFee.toStringAsFixed(0)}'),
                  _buildDetailRow('Net Amount Received',
                      '₦${netAmount.toStringAsFixed(0)}'),
                ],
              ),
            ),

            const Gap(20),

            // Done button
            Padding(
              padding: const EdgeInsets.all(16),
              child: SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xffB10303),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'Done',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {bool isStatus = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 10,
              fontWeight: FontWeight.w300,
              color: const Color(0xff8C8C8C),
            ),
          ),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: isStatus
                  ? (value.toLowerCase() == 'successful'
                      ? const Color(0xff2E7D32)
                      : value.toLowerCase() == 'pending'
                          ? const Color(0xffFBBC02)
                          : value.toLowerCase() == 'failed'
                              ? const Color(0xffB10303)
                              : Colors.white)
                  : Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
