import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';

class RecentHistory extends StatelessWidget {
  final String orderId;
  final String transactionMessage;
  final String receivedBy;
  final String date;
  final String time;
  final String status;
  final Color statusColor;
  final double amount;
  // Additional fields for transaction details
  final String packageName;
  final int quantity;
  final String paymentMethod;
  final double deliveryFee;

  const RecentHistory({
    super.key,
    required this.orderId,
    required this.transactionMessage,
    required this.receivedBy,
    required this.date,
    required this.time,
    required this.status,
    required this.amount,
    this.statusColor =
        const Color(0xff2E7D32), // Default green color for "Successful"
    this.packageName = 'Bag Of Rice', // Default value
    this.quantity = 1, // Default value
    this.paymentMethod = 'Bank Transfer', // Default value
    this.deliveryFee = 5000, // Default value
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _showTransactionDetails(context),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 7.5, vertical: 7),
        decoration: BoxDecoration(
          color: const Color(0xff1E1E1E),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Left side - Icon
            SvgPicture.asset(
              'assets/icons/pos.svg', // Make sure this icon exists or replace with appropriate icon
            ),
            const Gap(5),
            // Middle - Order info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Order ID
                  Text(
                    'Order ID: $orderId',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),

                  // Transaction message
                  Text(
                    transactionMessage,
                    style: GoogleFonts.poppins(
                      fontSize: 10,
                      fontWeight: FontWeight.w300,
                      color: const Color(0xff8C8C8C),
                    ),
                  ),

                  // Received by and date
                  Text(
                    '$receivedBy • $date, $time',
                    style: GoogleFonts.poppins(
                      fontSize: 10,
                      fontWeight: FontWeight.w300,
                      color: const Color(0xff8C8C8C),
                    ),
                  ),
                ],
              ),
            ),

            // Right side - Amount and status
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // Amount
                Text(
                  '₦${amount.toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
                const Gap(4),
                // Status button
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    status,
                    style: GoogleFonts.poppins(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showTransactionDetails(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.symmetric(horizontal: 20),
        child: Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: const Color(0xff1E1E1E),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Text(
                      'Transaction Details',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                    const Gap(5),
                    Text(
                      '$date, $time',
                      style: GoogleFonts.poppins(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xff8C8C8C),
                      ),
                    ),
                  ],
                ),
              ),
              const Gap(16),
              // Details
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    _buildDetailRow('Package ID', orderId),
                    _buildDetailRow('Package', packageName),
                    _buildDetailRow('Quantity', quantity.toString()),
                    _buildDetailRow('Receiver\'s Name', receivedBy),
                    _buildDetailRow('Payment Method', paymentMethod),
                    _buildDetailRow('Status', status, isStatus: true),
                    _buildDetailRow('Total Amount Paid by Customer',
                        '₦${amount.toStringAsFixed(0)}'),
                    _buildDetailRow('Delivery Fee Deducted',
                        '₦${deliveryFee.toStringAsFixed(0)}'),
                    _buildDetailRow('Net Amount Received',
                        '₦${(amount - deliveryFee).toStringAsFixed(0)}'),
                  ],
                ),
              ),

              const Gap(20),

              // Done button
              Padding(
                padding: const EdgeInsets.all(16),
                child: SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xffB10303),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Done',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {bool isStatus = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 10,
              fontWeight: FontWeight.w300,
              color: const Color(0xff8C8C8C),
            ),
          ),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: isStatus
                  ? (value.toLowerCase() == 'successful'
                      ? const Color(0xff2E7D32)
                      : value.toLowerCase() == 'pending'
                          ? const Color(0xffFBBC02)
                          : value.toLowerCase() == 'failed'
                              ? const Color(0xffB10303)
                              : Colors.white)
                  : Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
