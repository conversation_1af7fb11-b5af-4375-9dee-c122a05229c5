import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mialamobile/components/search_input.dart';
import 'package:mialamobile/components/text_dropdown.dart';
import 'package:mialamobile/dashboard/history/components/recent_history.dart';

class TransactionHistory extends StatefulWidget {
  const TransactionHistory({super.key});

  @override
  State<TransactionHistory> createState() => _TransactionHistoryState();
}

class _TransactionHistoryState extends State<TransactionHistory> {
  final List<Map<String, dynamic>> _listTransaction = [
    {
      'orderId': 'DCV4387900',
      'transactionMessage': 'Your Transaction was Received By',
      'receivedBy': '<PERSON>',
      'date': '12 Mar',
      'time': '05:30 AM',
      'status': 'Successful',
      'amount': 50000.00,
      'statusColor': const Color(0xff2E7D32),
      'packageName': 'Bag Of Rice',
      'quantity': 1,
      'paymentMethod': 'Bank Transfer',
      'deliveryFee': 5000.00,
    },
    {
      'orderId': 'DCV4387901',
      'transactionMessage': 'Your Transaction was Received By',
      'receivedBy': 'Lara Paul',
      'date': '13 Mar',
      'time': '05:30 AM',
      'status': 'Pending',
      'amount': 3000.00,
      'statusColor': const Color(0xffFBBC02),
      'packageName': 'Bag Of Beans',
      'quantity': 1,
      'paymentMethod': 'Bank Transfer',
      'deliveryFee': 500.00,
    },
    {
      'orderId': 'DCV4387901',
      'transactionMessage': 'Your Transaction was Received By',
      'receivedBy': 'Sarah Paul',
      'date': '15 Mar',
      'time': '05:30 AM',
      'status': 'Failed',
      'amount': 30000.00,
      'statusColor': const Color(0xffB10303),
      'packageName': 'Home appliances',
      'quantity': 1,
      'paymentMethod': 'Bank Transfer',
      'deliveryFee': 500.00,
    },
    {
      'orderId': 'DCV4387900',
      'transactionMessage': 'Your Transaction was Received By',
      'receivedBy': 'Jake Paul',
      'date': '12 Mar',
      'time': '05:30 AM',
      'status': 'Successful',
      'amount': 50000.00,
      'statusColor': const Color(0xff2E7D32),
      'packageName': 'Bag Of Rice',
      'quantity': 1,
      'paymentMethod': 'Bank Transfer',
      'deliveryFee': 5000.00,
    },
    {
      'orderId': 'DCV4387901',
      'transactionMessage': 'Your Transaction was Received By',
      'receivedBy': 'Lara Paul',
      'date': '13 Mar',
      'time': '05:30 AM',
      'status': 'Pending',
      'amount': 3000.00,
      'statusColor': const Color(0xffFBBC02),
      'packageName': 'Bag Of Beans',
      'quantity': 1,
      'paymentMethod': 'Bank Transfer',
      'deliveryFee': 500.00,
    },
    {
      'orderId': 'DCV4387901',
      'transactionMessage': 'Your Transaction was Received By',
      'receivedBy': 'Sarah Paul',
      'date': '15 Mar',
      'time': '05:30 AM',
      'status': 'Failed',
      'amount': 30000.00,
      'statusColor': const Color(0xffB10303),
      'packageName': 'Home appliances',
      'quantity': 1,
      'paymentMethod': 'Bank Transfer',
      'deliveryFee': 500.00,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xff121212),
      body: SafeArea(
          child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Transaction History',
                style: GoogleFonts.poppins(
                  fontSize: 24,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
              ),
              const Gap(20),
              SearchInput(
                hintText: '',
                suffixIcon: Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: SvgPicture.asset(
                    'assets/icons/search.svg',
                  ),
                ),
                prefixIcon: Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: SvgPicture.asset(
                    'assets/icons/preference.svg',
                  ),
                ),
                focusedBorderColor: Colors.transparent,
              ),
              const Gap(5),
              TextDropdown(
                value: 'Jan',
                items: const ['Jan', 'Feb', 'Mar', 'Apr'],
                onChanged: (newValue) {},
                dropdownColor:
                    const Color(0xff121212), // Dark background from memory
                dropdownWidth: 120,
                selectedItemColor: const Color(0xffB10303), // Primary red
                itemColor: Colors.white,
              ),
              const Gap(5),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text('Total:',
                          style: GoogleFonts.poppins(
                              textStyle: const TextStyle(
                                  color: Color(0xff8e8e8e),
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500))),
                      const Gap(4),
                      Text('₦250,000.00',
                          style: GoogleFonts.poppins(
                              textStyle: const TextStyle(
                                  color: Color(0xffFFFFFF),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500))),
                    ],
                  ),
                  const Gap(8),
                  Row(
                    children: [
                      Text('Delivery Fee:',
                          style: GoogleFonts.poppins(
                              textStyle: const TextStyle(
                                  color: Color(0xff8e8e8e),
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500))),
                      const Gap(4),
                      Text('₦150,000.00',
                          style: GoogleFonts.poppins(
                              textStyle: const TextStyle(
                                  color: Color(0xffFFFFFF),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500))),
                    ],
                  ),
                ],
              ),
              const Gap(18),
              ..._listTransaction.map((delivery) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: RecentHistory(
                    orderId: delivery['orderId'],
                    transactionMessage: delivery['transactionMessage'],
                    receivedBy: delivery['receivedBy'],
                    date: delivery['date'],
                    time: delivery['time'],
                    status: delivery['status'],
                    amount: delivery['amount'],
                    statusColor: delivery['statusColor'],
                  ),
                );
              }),
            ],
          ),
        ),
      )),
    );
  }
}
