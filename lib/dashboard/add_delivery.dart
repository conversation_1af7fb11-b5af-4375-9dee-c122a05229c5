import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mialamobile/components/input_dropdown.dart';
import 'package:mialamobile/components/input_type.dart';
import 'package:mialamobile/components/primary_button.dart';

class AddDelivery extends StatefulWidget {
  const AddDelivery({super.key});

  @override
  State<AddDelivery> createState() => _AddDeliveryState();
}

class _AddDeliveryState extends State<AddDelivery> {
  String? selectedQuantity;
  String? selectedState;
  String? selectedTown;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xff121212),
      appBar: AppBar(
        backgroundColor: const Color(0xff121212),
        iconTheme: const IconThemeData(color: Color(0XFF8E8E8E)),
        title: Text(
          'New Delivery',
          style: GoogleFonts.raleway(
            textStyle: const TextStyle(
                fontSize: 14, fontWeight: FontWeight.w500, color: Colors.white),
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
          child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 36),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Expanded(
                      child: InputType(
                          labelText: 'Package', hintText: 'Enter Product')),
                  const Gap(21),
                  Expanded(
                    child: InputDropdown(
                      labelText: 'Quantity',
                      hintText: '1',
                      value: selectedQuantity,
                      items: const [
                        DropdownMenuItem(value: '1', child: Text('1')),
                        DropdownMenuItem(value: '2', child: Text('2')),
                        DropdownMenuItem(value: '3', child: Text('3')),
                        DropdownMenuItem(value: '4', child: Text('4')),
                      ],
                      onChanged: (value) {
                        setState(() {
                          selectedQuantity = value;
                        });
                      },
                      isLoading: false,
                    ),
                  ),
                ],
              ),
              const Gap(20),
              const InputType(labelText: 'Price', hintText: 'Enter Price'),
              const Gap(20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: InputDropdown(
                      labelText: 'State',
                      hintText: 'Select State',
                      value: selectedState,
                      items: const [
                        DropdownMenuItem(value: 'lagos', child: Text('Lagos')),
                        DropdownMenuItem(value: 'abuja', child: Text('Abuja')),
                        DropdownMenuItem(
                            value: 'kaduna', child: Text('Kaduna')),
                        DropdownMenuItem(value: 'enugu', child: Text('Enugu')),
                      ],
                      onChanged: (value) {
                        setState(() {
                          selectedState = value;
                        });
                      },
                      isLoading: false,
                    ),
                  ),
                  const Gap(21),
                  Expanded(
                    child: InputDropdown(
                      labelText: 'Town',
                      hintText: 'Select Town',
                      value: selectedTown,
                      items: const [
                        DropdownMenuItem(value: 'ajah', child: Text('Ajah')),
                        DropdownMenuItem(value: 'lekki', child: Text('Lekki')),
                        DropdownMenuItem(
                            value: 'mushin', child: Text('Mushin')),
                        DropdownMenuItem(value: 'ikeja', child: Text('Ikeja')),
                      ],
                      onChanged: (value) {
                        setState(() {
                          selectedTown = value;
                        });
                      },
                      isLoading: false,
                    ),
                  ),
                ],
              ),
              const Gap(20),
              const InputType(
                  labelText: 'Location', hintText: 'Enter Location'),
              const Gap(20),
              const InputType(
                  labelText: 'Reciever Name',
                  hintText: "Enter Reciever's Name"),
              const Gap(20),
              const InputType(
                  labelText: 'Delivery Fee', hintText: 'Enter Delivery Fee'),
              const Gap(20),
              PrimaryButton(
                  text: 'Done', onPressed: () {}, width: double.infinity)
            ],
          ),
        ),
      )),
    );
  }
}
