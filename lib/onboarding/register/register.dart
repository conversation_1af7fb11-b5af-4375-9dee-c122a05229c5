import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mialamobile/components/input_dropdown.dart';
import 'package:mialamobile/components/input_type.dart';
import 'package:mialamobile/components/primary_button.dart';
import 'package:mialamobile/models/bank.dart';
import 'package:mialamobile/models/state.dart' as app_state;
import 'package:mialamobile/onboarding/login/login_page.dart';
import 'package:mialamobile/onboarding/login/otp_verification.dart';
import 'package:provider/provider.dart';
import 'package:mialamobile/providers/auth_provider.dart';
import 'dart:developer' as developer;

class RegistrationPage extends StatefulWidget {
  const RegistrationPage({super.key});

  @override
  State<RegistrationPage> createState() => _RegistrationPageState();
}

class _RegistrationPageState extends State<RegistrationPage> {
  // Constants
  static const _backgroundColor = Color(0xff121212);
  static const _primaryColor = Color(0xffFF0000);
  static const _greyTextColor = Color(0xff8C8C8C);
  static const _hintColor = Color(0xFFAAAAAA);

  // Form key for validation
  final _formKey = GlobalKey<FormState>();

  // Controllers for form fields
  final _firstnameController = TextEditingController();
  final _lastnameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _accountNumberController = TextEditingController();
  final _accountNameController = TextEditingController();
  final _bankNameController =
      TextEditingController(); // Will store the bank name for display
  final _ninController = TextEditingController();

  // For password visibility
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  // Selected bank and state
  String? _selectedBankCode;
  String? _selectedState;

  // Loading and error states
  bool _isLoading = false;
  bool _isVerifyingBankAccount = false;
  bool _isLoadingBanks = false;
  bool _isLoadingStates = false;
  String? _errorMessage;

  // Store dropdown items to avoid rebuilding them in the build method
  final List<DropdownMenuItem<String>> _bankItems = [];
  final List<DropdownMenuItem<String>> _stateItems = [];

  // Field validation states - initialize with empty strings to avoid showing errors on page load
  final Map<String, String> _fieldErrors = {
    'firstName': '',
    'lastName': '',
    'email': '',
    'phone': '',
    'password': '',
    'confirmPassword': '',
    'accountNumber': '',
    'accountName': '',
    'bankName': '',
    'state': '',
  };

  @override
  void initState() {
    super.initState();

    _setupTextFieldListeners();

    // Fetch banks and states lists after the widget is fully built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _fetchBanks();
        _fetchStates();
      }
    });
  }

  // Setup listeners for all text fields
  void _setupTextFieldListeners() {
    // Basic field validation listeners
    _firstnameController.addListener(() => _validateField('firstName'));
    _lastnameController.addListener(() => _validateField('lastName'));
    _emailController.addListener(() => _validateField('email'));
    _phoneController.addListener(() => _validateField('phone'));

    // Password field with special handling for confirm password
    _passwordController.addListener(() {
      _validateField('password');
      if (_confirmPasswordController.text.isNotEmpty) {
        _validateField('confirmPassword');
      }
    });

    _confirmPasswordController
        .addListener(() => _validateField('confirmPassword'));

    // Account number with automatic verification
    _accountNumberController.addListener(() {
      _validateField('accountNumber');

      // Auto-verify when account number is valid and bank is selected
      if (_accountNumberController.text.length == 10 &&
          _selectedBankCode != null) {
        Future.microtask(() => _verifyBankAccount());
      }
    });

    _accountNameController.addListener(() => _validateField('accountName'));
    _ninController.addListener(() => _validateField('state'));
  }

  // Fetch banks list from API
  Future<void> _fetchBanks() async {
    developer.log('Starting to fetch banks list', name: 'register_page');

    // Avoid state changes during build phase
    if (WidgetsBinding.instance.schedulerPhase ==
        SchedulerPhase.persistentCallbacks) {
      developer.log('In build phase, delaying state change',
          name: 'register_page');
      await Future.microtask(() {});
    }

    if (!mounted) return;

    setState(() {
      _isLoadingBanks = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final result = await authProvider.fetchBanks();

      developer.log('Banks list fetch result: $result', name: 'register_page');
      developer.log('Banks count: ${authProvider.banks.length}',
          name: 'register_page');

      if (!mounted) return;

      setState(() {
        _isLoadingBanks = false;
      });

      // Update bank items list for dropdown
      _updateBankItems(authProvider);

      // Handle response based on success and cache status
      final usingCache = result['usingCache'] == true;
      if (!result['success'] || usingCache) {
        _showBanksListFetchMessage(
          message: result['message'] ?? 'Failed to fetch banks list',
          isWarning: usingCache,
        );
      }
    } catch (e) {
      developer.log('Error fetching banks list: $e', name: 'register_page');
      developer.log(
          'Error stack trace: ${e is Error ? e.stackTrace : "No stack trace"}',
          name: 'register_page');

      if (!mounted) return;

      setState(() {
        _isLoadingBanks = false;
      });

      // Update bank items list to show error state
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      _updateBankItems(authProvider);

      // Show error message
      _showBanksListFetchMessage(
        message: 'Failed to fetch banks list',
        isWarning: false,
      );
    }
  }

  // Show message for banks list fetch result
  void _showBanksListFetchMessage(
      {required String message, required bool isWarning}) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: isWarning ? Colors.orange : Colors.red,
            duration: const Duration(seconds: 3),
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: _fetchBanks,
            ),
          ),
        );
      }
    });
  }

  // Update bank items list for dropdown
  void _updateBankItems(AuthProvider authProvider) {
    developer.log('Updating bank items list', name: 'register_page');
    developer.log('Loading state: $_isLoadingBanks', name: 'register_page');
    developer.log('Banks count: ${authProvider.banks.length}',
        name: 'register_page');

    _bankItems.clear();

    if (_isLoadingBanks) return;

    // Handle empty banks list
    if (authProvider.banks.isEmpty) {
      _addEmptyBankItem();
      return;
    }

    // Add default selection option
    _addDefaultBankItem();

    // Add sorted bank items
    _addSortedBankItems(authProvider.banks);
  }

  // Add "No banks available" item when list is empty
  void _addEmptyBankItem() {
    developer.log('Adding "No banks available" item', name: 'register_page');
    _bankItems.add(const DropdownMenuItem(
      value: null,
      child: Text(
        'No banks available',
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
        style: TextStyle(
          fontSize: 14.0,
          fontWeight: FontWeight.w400,
          color: Color(0xFFAAAAAA),
        ),
      ),
    ));
  }

  // Add default "Select your bank" option
  void _addDefaultBankItem() {
    developer.log('Adding default "Select your bank" option',
        name: 'register_page');
    _bankItems.add(const DropdownMenuItem(
      value: null,
      child: Text(
        'Select your bank',
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
        style: TextStyle(
          fontSize: 14.0,
          fontWeight: FontWeight.w400,
          color: Color(0xFFAAAAAA),
        ),
      ),
    ));
  }

  // Add sorted bank items to dropdown
  void _addSortedBankItems(List<Bank> banks) {
    developer.log('Adding ${banks.length} banks to dropdown',
        name: 'register_page');

    // Sort banks alphabetically by name
    final sortedBanks = List<Bank>.from(banks);
    sortedBanks.sort((a, b) => a.name.compareTo(b.name));

    // Add all banks from the API
    _bankItems.addAll(
      sortedBanks.map((bank) {
        return DropdownMenuItem<String>(
          value: bank.code,
          child: Text(
            bank.name,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            style: const TextStyle(
              fontSize: 14.0,
              fontWeight: FontWeight.w400,
              color: Colors.white,
            ),
          ),
        );
      }).toList(),
    );

    // Log sample banks for debugging
    if (sortedBanks.isNotEmpty) {
      final sampleBanks = sortedBanks.take(3).toList();
      developer.log(
          'Sample banks: ${sampleBanks.map((b) => "${b.name} (${b.code})").join(", ")}',
          name: 'register_page');
    }

    developer.log('Bank items list updated with ${_bankItems.length} items',
        name: 'register_page');
  }

  // Handle bank selection outside of build method
  void _handleBankSelection(String? value) {
    setState(() {
      _selectedBankCode = value;

      // Update bank name display and clear errors
      if (value != null) {
        _updateBankNameFromSelection(value);
      } else {
        _bankNameController.text = '';
      }
    });

    // Trigger bank account verification if possible
    if (value != null && _accountNumberController.text.length == 10) {
      Future.microtask(() => _verifyBankAccount());
    }
  }

  // Update bank name from selected bank code
  void _updateBankNameFromSelection(String bankCode) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final selectedBank = authProvider.banks.firstWhere(
      (bank) => bank.code == bankCode,
      orElse: () => Bank(name: '', code: ''),
    );

    _bankNameController.text = selectedBank.name;
    _fieldErrors['bankName'] = '';
  }

  // Fetch states list from API
  Future<void> _fetchStates() async {
    developer.log('Starting to fetch states list', name: 'register_page');

    // Avoid state changes during build phase
    if (WidgetsBinding.instance.schedulerPhase ==
        SchedulerPhase.persistentCallbacks) {
      developer.log('In build phase, delaying state change',
          name: 'register_page');
      await Future.microtask(() {});
    }

    if (!mounted) return;

    setState(() {
      _isLoadingStates = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final result = await authProvider.fetchStates();

      developer.log('States list fetch result: $result', name: 'register_page');
      developer.log('States count: ${authProvider.states.length}',
          name: 'register_page');

      if (!mounted) return;

      setState(() {
        _isLoadingStates = false;
      });

      // Update state items list for dropdown
      _updateStateItems(authProvider);

      // Handle response based on success and cache status
      final usingCache = result['usingCache'] == true;
      if (!result['success'] || usingCache) {
        _showStatesListFetchMessage(
          message: result['message'] ?? 'Failed to fetch states list',
          isWarning: usingCache,
        );
      }
    } catch (e) {
      developer.log('Error fetching states list: $e', name: 'register_page');
      developer.log(
          'Error stack trace: ${e is Error ? e.stackTrace : "No stack trace"}',
          name: 'register_page');

      if (!mounted) return;

      setState(() {
        _isLoadingStates = false;
      });

      // Update state items list to show error state
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      _updateStateItems(authProvider);

      // Show error message
      _showStatesListFetchMessage(
        message: 'Failed to fetch states list',
        isWarning: false,
      );
    }
  }

  // Show message for states list fetch result
  void _showStatesListFetchMessage(
      {required String message, required bool isWarning}) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: isWarning ? Colors.orange : Colors.red,
            duration: const Duration(seconds: 3),
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: _fetchStates,
            ),
          ),
        );
      }
    });
  }

  // Update state items list for dropdown
  void _updateStateItems(AuthProvider authProvider) {
    developer.log('Updating state items list', name: 'register_page');
    developer.log('Loading state: $_isLoadingStates', name: 'register_page');
    developer.log('States count: ${authProvider.states.length}',
        name: 'register_page');

    _stateItems.clear();

    if (_isLoadingStates) return;

    // Handle empty states list
    if (authProvider.states.isEmpty) {
      _addEmptyStateItem();
      return;
    }

    // Add default selection option
    _addDefaultStateItem();

    // Add sorted state items
    _addSortedStateItems(authProvider.states);
  }

  // Add "No states available" item when list is empty
  void _addEmptyStateItem() {
    developer.log('Adding "No states available" item', name: 'register_page');
    _stateItems.add(const DropdownMenuItem(
      value: null,
      child: Text(
        'No states available',
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
        style: TextStyle(
          fontSize: 14.0,
          fontWeight: FontWeight.w400,
          color: Color(0xFFAAAAAA),
        ),
      ),
    ));
  }

  // Add default "Select your state" option
  void _addDefaultStateItem() {
    developer.log('Adding default "Select your state" option',
        name: 'register_page');
    _stateItems.add(const DropdownMenuItem(
      value: null,
      child: Text(
        'Select your state',
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
        style: TextStyle(
          fontSize: 14.0,
          fontWeight: FontWeight.w400,
          color: Color(0xFFAAAAAA),
        ),
      ),
    ));
  }

  // Add sorted state items to dropdown
  void _addSortedStateItems(List<app_state.State> states) {
    developer.log('Adding ${states.length} states to dropdown',
        name: 'register_page');

    // Sort states alphabetically by name
    final sortedStates = List<app_state.State>.from(states);
    sortedStates.sort((a, b) => a.name.compareTo(b.name));

    // Add all states from the API
    _stateItems.addAll(
      sortedStates.map((state) {
        return DropdownMenuItem<String>(
          value: state.name,
          child: Text(
            state.name,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            style: const TextStyle(
              fontSize: 14.0,
              fontWeight: FontWeight.w400,
              color: Colors.white,
            ),
          ),
        );
      }).toList(),
    );

    // Log sample states for debugging
    if (sortedStates.isNotEmpty) {
      final sampleStates = sortedStates.take(3).toList();
      developer.log(
          'Sample states: ${sampleStates.map((s) => s.name).join(", ")}',
          name: 'register_page');
    }

    developer.log('State items list updated with ${_stateItems.length} items',
        name: 'register_page');
  }

  // Handle state selection outside of build method
  void _handleStateSelection(String? value) {
    setState(() {
      _selectedState = value;

      // Clear state validation error if a state is selected
      if (value != null) {
        _fieldErrors['state'] = '';
      }
    });
  }

  @override
  void dispose() {
    // Remove listeners from all controllers
    _removeTextFieldListeners();

    // Dispose all controllers
    _disposeControllers();

    // Clear dropdown items lists
    _bankItems.clear();
    _stateItems.clear();

    super.dispose();
  }

  // Remove all text field listeners
  void _removeTextFieldListeners() {
    _firstnameController.removeListener(() => _validateField('firstName'));
    _lastnameController.removeListener(() => _validateField('lastName'));
    _emailController.removeListener(() => _validateField('email'));
    _phoneController.removeListener(() => _validateField('phone'));
    _passwordController.removeListener(() => _validateField('password'));
    _confirmPasswordController
        .removeListener(() => _validateField('confirmPassword'));

    // Remove all listeners from account number controller
    _accountNumberController.removeListener(() {});

    _accountNameController.removeListener(() => _validateField('accountName'));
    _ninController.removeListener(() => _validateField('state'));
  }

  // Dispose all controllers
  void _disposeControllers() {
    _firstnameController.dispose();
    _lastnameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _accountNumberController.dispose();
    _accountNameController.dispose();
    _bankNameController.dispose();
    _ninController.dispose();
  }

  // Validate individual field and update error state
  void _validateField(String fieldName) {
    String errorMessage = '';

    switch (fieldName) {
      case 'firstName':
        errorMessage = _validateFirstName();
        break;

      case 'lastName':
        errorMessage = _validateLastName();
        break;

      case 'email':
        errorMessage = _validateEmail();
        break;

      case 'phone':
        errorMessage = _validatePhone();
        break;

      case 'password':
        errorMessage = _validatePassword();
        break;

      case 'confirmPassword':
        errorMessage = _validateConfirmPassword();
        break;

      case 'accountNumber':
        errorMessage = _validateAccountNumber();
        break;

      case 'accountName':
        errorMessage = _validateAccountName();
        break;

      case 'bankName':
        errorMessage = _validateBankName();
        break;

      case 'state':
        errorMessage = _validateNIN();
        break;
    }

    setState(() {
      _fieldErrors[fieldName] = errorMessage;
    });
  }

  // Validation methods for each field
  String _validateFirstName() {
    if (_firstnameController.text.isEmpty) {
      return 'First name is required';
    }
    return '';
  }

  String _validateLastName() {
    if (_lastnameController.text.isEmpty) {
      return 'Last name is required';
    }
    return '';
  }

  String _validateEmail() {
    if (_emailController.text.isEmpty) {
      return 'Email is required';
    }

    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(_emailController.text)) {
      return 'Please enter a valid email address';
    }

    return '';
  }

  String _validatePhone() {
    if (_phoneController.text.isEmpty) {
      return 'Phone number is required';
    }

    // Updated regex to accept either 10 or 11 digits
    final phoneRegex = RegExp(r'^\d{10,11}$');
    if (!phoneRegex.hasMatch(_phoneController.text)) {
      return 'Please enter a valid 10 or 11-digit phone number';
    }

    return '';
  }

  String _validatePassword() {
    if (_passwordController.text.isEmpty) {
      return 'Password is required';
    }

    if (_passwordController.text.length < 8) {
      return 'Password must be at least 8 characters';
    }

    // Check if password contains at least one number and one letter
    final hasNumber = RegExp(r'[0-9]').hasMatch(_passwordController.text);
    final hasLetter = RegExp(r'[a-zA-Z]').hasMatch(_passwordController.text);

    if (!hasNumber || !hasLetter) {
      return 'Password must contain both letters and numbers';
    }

    // If password is valid, also update confirmPassword validation
    if (_confirmPasswordController.text.isNotEmpty) {
      if (_confirmPasswordController.text != _passwordController.text) {
        setState(() {
          _fieldErrors['confirmPassword'] = 'Passwords do not match';
        });
      } else {
        setState(() {
          _fieldErrors['confirmPassword'] = '';
        });
      }
    }

    return '';
  }

  String _validateConfirmPassword() {
    if (_confirmPasswordController.text.isEmpty) {
      return 'Please confirm your password';
    }

    if (_confirmPasswordController.text != _passwordController.text) {
      return 'Passwords do not match';
    }

    return '';
  }

  String _validateAccountNumber() {
    if (_accountNumberController.text.isEmpty) {
      return 'Account number is required';
    }

    final accountNumberRegex = RegExp(r'^\d{10}$');
    if (!accountNumberRegex.hasMatch(_accountNumberController.text)) {
      return 'Please enter a valid 10-digit account number';
    }

    return '';
  }

  String _validateAccountName() {
    if (_accountNameController.text.isEmpty) {
      return 'Account name is required';
    }
    return '';
  }

  String _validateBankName() {
    if (_selectedBankCode == null || _selectedBankCode!.isEmpty) {
      return 'Bank selection is required';
    }
    return '';
  }

  String _validateNIN() {
    // If we're using the state dropdown instead of NIN field
    if (_selectedState == null) {
      return 'State selection is required';
    }

    // For backward compatibility, keep the NIN validation
    if (_ninController.text.isEmpty) {
      return 'NIN is required';
    }

    final ninRegex = RegExp(r'^\d{11}$');
    if (!ninRegex.hasMatch(_ninController.text)) {
      return 'Please enter a valid 11-digit NIN';
    }

    return '';
  }

  // Verify bank account automatically
  Future<void> _verifyBankAccount() async {
    // Only proceed if both account number and bank code are valid
    if (_accountNumberController.text.length != 10 ||
        _selectedBankCode == null ||
        !mounted) {
      return;
    }

    developer.log('Auto-verifying bank account', name: 'register_page');
    developer.log('Account number: ${_accountNumberController.text.trim()}',
        name: 'register_page');
    developer.log('Bank code: $_selectedBankCode', name: 'register_page');

    // Avoid state changes during build phase
    if (WidgetsBinding.instance.schedulerPhase ==
        SchedulerPhase.persistentCallbacks) {
      await Future.microtask(() {});
    }

    if (!mounted) return;

    setState(() {
      _isVerifyingBankAccount = true;
      // Clear account name field while verifying
      _accountNameController.text = '';
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Call the verify bank account API
      final verificationResult = await authProvider.verifyBankAccount(
        accountNumber: _accountNumberController.text.trim(),
        bankCode: _selectedBankCode!,
      );

      developer.log('Auto bank verification API call completed',
          name: 'register_page');
      developer.log(
          'Auto verification result success: ${verificationResult['success']}',
          name: 'register_page');
      developer.log(
          'Auto verification result data: ${verificationResult['data']}',
          name: 'register_page');

      if (!mounted) return;

      _handleBankVerificationResult(verificationResult);

      // Ensure the account name field is updated in the UI
      if (_accountNameController.text.isNotEmpty) {
        developer.log(
            'Account name after verification: ${_accountNameController.text}',
            name: 'register_page');
      } else {
        developer.log('Account name is still empty after verification',
            name: 'register_page');
      }
    } catch (e) {
      developer.log('Exception during auto bank verification: $e',
          name: 'register_page');

      if (mounted) {
        _showErrorSnackBar(
            'An error occurred while verifying the bank account');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isVerifyingBankAccount = false;
        });
      }
    }
  }

  // Handle bank verification API result
  void _handleBankVerificationResult(Map<String, dynamic> result) {
    developer.log('Bank verification result: $result', name: 'register_page');

    if (result['success']) {
      // If verification is successful, update the account name
      if (result['accountName'] != null &&
          result['accountName'].toString().isNotEmpty) {
        developer.log('Setting account name to: ${result['accountName']}',
            name: 'register_page');

        setState(() {
          _accountNameController.text = result['accountName'];
        });

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Bank account verified successfully'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      } else {
        // Try to extract account name from data field if available
        final data = result['data'];
        if (data != null && data is Map && data.containsKey('data')) {
          final accountName = data['data']?['account_name'];
          if (accountName != null && accountName.toString().isNotEmpty) {
            developer.log('Extracted account name from data: $accountName',
                name: 'register_page');

            setState(() {
              _accountNameController.text = accountName.toString();
            });

            // Show success message
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Bank account verified successfully'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 2),
              ),
            );
          } else {
            developer.log('Account name not found in data field',
                name: 'register_page');
          }
        } else {
          developer.log('Data field not found or invalid in result',
              name: 'register_page');
        }
      }
    } else {
      // If verification fails, show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content:
              Text(result['message'] ?? 'Bank account verification failed'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  // Show error snackbar with standard styling
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  // Check if all fields are valid
  bool _isFormValid() {
    // Check if all required fields have values
    if (_areRequiredFieldsEmpty()) {
      return false;
    }

    // Check if any field has an error (non-empty error message)
    return !_fieldErrors.values.any((error) => error.isNotEmpty);
  }

  // Check if any required fields are empty
  bool _areRequiredFieldsEmpty() {
    return _firstnameController.text.isEmpty ||
        _lastnameController.text.isEmpty ||
        _emailController.text.isEmpty ||
        _phoneController.text.isEmpty ||
        _passwordController.text.isEmpty ||
        _confirmPasswordController.text.isEmpty ||
        _accountNumberController.text.isEmpty ||
        _accountNameController.text.isEmpty ||
        _bankNameController.text.isEmpty ||
        _selectedState == null;
    // || _ninController.text.isEmpty
  }

  // Get the onPressed handler for the Done button
  // This avoids calling _isFormValid() during build
  VoidCallback? _getOnPressedHandler() {
    final isValid = _isFormValid();
    return isValid ? () => _handleSignup() : null;
  }

  // Validate form fields and handle signup
  Future<void> _handleSignup() async {
    developer.log('Register button pressed', name: 'register_page');

    // Reset error message
    setState(() {
      _errorMessage = null;
    });

    // Validate all required fields
    _validateAllFields();

    // Check if form is valid
    if (!_isFormValid()) {
      _handleFormValidationFailure();
      return;
    }

    developer.log('Form validation successful', name: 'register_page');

    // Set loading state
    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // First verify the bank account
      developer.log('Verifying bank account', name: 'register_page');

      // Check if bank code is selected
      if (_selectedBankCode == null) {
        _handleMissingBankSelection();
        return;
      }

      // Verify bank account
      final verificationResult =
          await _verifyBankAccountForSignup(authProvider);
      if (!verificationResult['success']) {
        _handleBankVerificationFailure(verificationResult);
        return;
      }

      // Update account name if available
      if (verificationResult['accountName'] != null &&
          verificationResult['accountName'].toString().isNotEmpty) {
        developer.log(
            'Setting account name from verification result: ${verificationResult['accountName']}',
            name: 'register_page');
        _accountNameController.text = verificationResult['accountName'];
      } else {
        // Try to extract account name from data field if available
        final data = verificationResult['data'];
        if (data != null && data is Map && data.containsKey('data')) {
          final accountName = data['data']?['account_name'];
          if (accountName != null && accountName.toString().isNotEmpty) {
            developer.log(
                'Extracted account name from data field: $accountName',
                name: 'register_page');
            _accountNameController.text = accountName.toString();
          } else {
            developer.log('Account name not found in data field during signup',
                name: 'register_page');
          }
        } else {
          developer.log(
              'Data field not found or invalid in verification result during signup',
              name: 'register_page');
        }
      }

      // Proceed with signup
      developer.log('Bank verification successful, proceeding with signup',
          name: 'register_page');
      final signupResult = await _performSignup(authProvider);

      // Handle signup result
      if (signupResult['success']) {
        _handleSuccessfulSignup(signupResult);
      } else {
        _handleFailedSignup(signupResult);
      }
    } catch (e) {
      _handleSignupException(e);
    } finally {
      // Reset loading state
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Validate all form fields
  void _validateAllFields() {
    _validateField('firstName');
    _validateField('lastName');
    _validateField('email');
    _validateField('phone');
    _validateField('password');
    _validateField('confirmPassword');
    _validateField('accountNumber');
    _validateField('accountName');
    _validateField('bankName');
    // _validateField('state');
  }

  // Handle form validation failure
  void _handleFormValidationFailure() {
    developer.log('Form validation failed', name: 'register_page');

    // Find the first error to display as the main error message
    final firstError = _fieldErrors.entries
        .firstWhere((entry) => entry.value.isNotEmpty,
            orElse: () =>
                const MapEntry('', 'Please fix the errors in the form'))
        .value;

    // Show validation error in a snackbar
    _showErrorSnackBar(firstError);

    setState(() {
      _errorMessage = firstError;
    });
  }

  // Handle missing bank selection
  void _handleMissingBankSelection() {
    setState(() {
      _errorMessage = 'Please select a bank';
      _isLoading = false;
    });

    _showErrorSnackBar('Please select a bank');
  }

  // Verify bank account for signup
  Future<Map<String, dynamic>> _verifyBankAccountForSignup(
      AuthProvider authProvider) async {
    final verificationResult = await authProvider.verifyBankAccount(
      accountNumber: _accountNumberController.text.trim(),
      bankCode: _selectedBankCode!,
    );

    developer.log('Bank verification API call completed',
        name: 'register_page');
    developer.log(
        'Verification result success: ${verificationResult['success']}',
        name: 'register_page');
    developer.log(
        'Verification result message: ${verificationResult['message']}',
        name: 'register_page');

    return verificationResult;
  }

  // Handle bank verification failure
  void _handleBankVerificationFailure(Map<String, dynamic> verificationResult) {
    developer.log('Bank verification failed: ${verificationResult['message']}',
        name: 'register_page');

    if (mounted) {
      _showErrorSnackBar(
          verificationResult['message'] ?? 'Bank account verification failed');

      setState(() {
        _errorMessage = verificationResult['message'];
        _isLoading = false;
      });
    }
  }

  // Perform signup API call
  Future<Map<String, dynamic>> _performSignup(AuthProvider authProvider) async {
    developer.log('Calling signup method with form data',
        name: 'register_page');

    final result = await authProvider.signup(
      firstName: _firstnameController.text.trim(),
      lastName: _lastnameController.text.trim(),
      email: _emailController.text.trim(),
      password: _passwordController.text.trim(),
      phone: _phoneController.text.trim(),
      accountNumber: _accountNumberController.text.trim(),
      accountName: _accountNameController.text.trim(),
      bankName: _selectedBankCode!, // Use the bank code for API call
      state: _selectedState!, // Use the selected state
      // state: _ninController.text.trim(),
    );

    developer.log('Signup API call completed', name: 'register_page');
    developer.log('Result success: ${result['success']}',
        name: 'register_page');
    developer.log('Result message: ${result['message']}',
        name: 'register_page');

    return result;
  }

  // Handle successful signup
  void _handleSuccessfulSignup(Map<String, dynamic> result) {
    developer.log('Registration successful', name: 'register_page');

    if (mounted) {
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result['message'] ?? 'Registration successful'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );

      // Navigate to OTP verification screen
      developer.log('Navigating to OTP verification screen',
          name: 'register_page');
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => OtpVerification(
            email: _emailController.text.trim(),
          ),
        ),
      );
    }
  }

  // Handle failed signup
  void _handleFailedSignup(Map<String, dynamic> result) {
    developer.log('Registration failed: ${result['message']}',
        name: 'register_page');

    if (mounted) {
      _showErrorSnackBar(result['message'] ?? 'Registration failed');

      setState(() {
        _errorMessage = result['message'];
      });
    }
  }

  // Handle exception during signup
  void _handleSignupException(dynamic exception) {
    developer.log('Exception during registration: $exception',
        name: 'register_page');

    if (mounted) {
      _showErrorSnackBar('An unexpected error occurred. Please try again.');

      setState(() {
        _errorMessage = 'An unexpected error occurred. Please try again.';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _backgroundColor,
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 32),
              child: Form(
                key: _formKey,
                child: Column(mainAxisSize: MainAxisSize.min, children: [
                  _buildHeader(),
                  const Gap(65),
                  _buildPersonalInfoFields(),
                  const Gap(10),
                  _buildPasswordFields(),
                  const Gap(10),
                  _buildBankAccountFields(),
                  const Gap(24),
                  _buildErrorMessage(),
                  _buildSubmitButton(),
                ]),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Build header with title and login link
  Widget _buildHeader() {
    return Column(
      children: [
        Text(
          'Create Account',
          style: GoogleFonts.inter(
            fontWeight: FontWeight.w700,
            fontSize: 24,
            color: Colors.white,
          ),
        ),
        const Gap(10),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Already have an account?',
              style: GoogleFonts.inter(
                fontWeight: FontWeight.w300,
                fontSize: 12,
                color: Colors.white,
              ),
            ),
            const Gap(4),
            InkWell(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const LoginPage(),
                  ),
                );
              },
              child: Text(
                'Login',
                style: GoogleFonts.inter(
                  fontWeight: FontWeight.w400,
                  fontSize: 12,
                  color: _primaryColor,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Build personal information fields (name, email, phone)
  Widget _buildPersonalInfoFields() {
    return Column(
      children: [
        InputType(
          labelText: 'First Name',
          hintText: 'John',
          keyboardType: TextInputType.name,
          controller: _firstnameController,
          errorText: _fieldErrors['firstName'],
        ),
        const Gap(10),
        InputType(
          labelText: 'Last Name',
          hintText: 'Doe',
          keyboardType: TextInputType.name,
          controller: _lastnameController,
          errorText: _fieldErrors['lastName'],
        ),
        const Gap(10),
        InputType(
          labelText: 'Email',
          hintText: '<EMAIL>',
          keyboardType: TextInputType.emailAddress,
          controller: _emailController,
          errorText: _fieldErrors['email'],
        ),
        const Gap(10),
        InputType(
          labelText: 'Mobile Number',
          hintText: 'Enter 10 or 11-digit phone number',
          keyboardType: TextInputType.phone,
          controller: _phoneController,
          errorText: _fieldErrors['phone'],
        ),
      ],
    );
  }

  // Build password and confirm password fields
  Widget _buildPasswordFields() {
    return Column(
      children: [
        // Password field with hint
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            InputType(
              labelText: 'Enter Password',
              hintText: '********',
              obscureText: _obscurePassword,
              suffixIcon: IconButton(
                icon: Icon(
                  _obscurePassword ? Icons.visibility_off : Icons.visibility,
                  size: 12,
                  color: _greyTextColor,
                ),
                onPressed: () {
                  setState(() {
                    _obscurePassword = !_obscurePassword;
                  });
                },
              ),
              controller: _passwordController,
              errorText: _fieldErrors['password'],
            ),
            if (_fieldErrors['password']?.isEmpty ?? true)
              Padding(
                padding: const EdgeInsets.only(left: 8.0, top: 4.0),
                child: Text(
                  'Password must be at least 8 characters with letters and numbers',
                  style: GoogleFonts.inter(
                    fontSize: 10,
                    color: _greyTextColor,
                  ),
                ),
              ),
          ],
        ),
        const Gap(10),
        // Confirm password field
        InputType(
          labelText: 'Confirm Password',
          hintText: '********',
          obscureText: _obscureConfirmPassword,
          suffixIcon: IconButton(
            icon: Icon(
              _obscureConfirmPassword ? Icons.visibility_off : Icons.visibility,
              size: 12,
              color: _greyTextColor,
            ),
            onPressed: () {
              setState(() {
                _obscureConfirmPassword = !_obscureConfirmPassword;
              });
            },
          ),
          controller: _confirmPasswordController,
          errorText: _fieldErrors['confirmPassword'],
        ),
      ],
    );
  }

  // Build bank account fields
  Widget _buildBankAccountFields() {
    return Column(
      children: [
        // Account number field
        InputType(
          labelText: 'Account Number',
          hintText: '10-digit account number',
          keyboardType: TextInputType.number,
          controller: _accountNumberController,
          errorText: _fieldErrors['accountNumber'],
        ),
        const Gap(10),
        // Bank dropdown
        _buildBankDropdown(),
        const Gap(10),
        // Account name field
        InputType(
          labelText: 'Account Name',
          hintText: _isVerifyingBankAccount
              ? 'Verifying account...'
              : 'Enter account name',
          keyboardType: TextInputType.name,
          controller: _accountNameController,
          errorText: _fieldErrors['accountName'],
          suffixIcon: _isVerifyingBankAccount
              ? Container(
                  padding: const EdgeInsets.all(10),
                  child: const SizedBox(
                    height: 5,
                    width: 5,
                    child: CircularProgressIndicator(
                      strokeWidth: 0.5,
                      color: Color(0xff8C8C8C),
                    ),
                  ),
                )
              : null,
          // Add key for testing and debugging
          key: const Key('accountNameField'),
        ),
        const Gap(10),
        // State dropdown
        _buildStateDropdown(),

        // NIN field (commented out)
        // InputType(
        //   labelText: 'NIN',
        //   hintText: 'Enter your 11-digit NIN',
        //   keyboardType: TextInputType.number,
        //   controller: _ninController,
        //   errorText: _fieldErrors['state'],
        // ),
      ],
    );
  }

  // Build state dropdown with loading indicator and retry button
  Widget _buildStateDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        InputDropdown(
          labelText: 'State',
          hintText: '',
          value: _selectedState,
          onChanged: _handleStateSelection,
          items: _stateItems,
          errorText: _fieldErrors['state'],
          hintColor: _hintColor,
          isLoading: _isLoadingStates,
          textColor: Colors.white,
        ),
        if (_isLoadingStates)
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: Text(
              'Fetching states list, please wait...',
              style: GoogleFonts.poppins(
                fontSize: 10,
                color: _greyTextColor,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        // Show retry button if states list is empty but not loading
        if (!_isLoadingStates &&
            Provider.of<AuthProvider>(context).states.isEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: _fetchStates,
                  child: const Text(
                    'Retry loading states',
                    style: TextStyle(
                      color: _primaryColor,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  // Build bank dropdown with loading indicator and retry button
  Widget _buildBankDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        InputDropdown(
          labelText: 'Bank Name',
          hintText: '',
          value: _selectedBankCode,
          onChanged: _handleBankSelection,
          items: _bankItems,
          errorText: _fieldErrors['bankName'],
          hintColor: _hintColor,
          isLoading: _isLoadingBanks,
          textColor: Colors.white,
        ),
        if (_isLoadingBanks)
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: Text(
              'Fetching banks list, please wait...',
              style: GoogleFonts.poppins(
                fontSize: 10,
                color: _greyTextColor,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        // Show retry button if banks list is empty but not loading
        if (!_isLoadingBanks &&
            Provider.of<AuthProvider>(context).banks.isEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: _fetchBanks,
                  child: const Text(
                    'Retry loading banks',
                    style: TextStyle(
                      color: _primaryColor,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  // Build error message display
  Widget _buildErrorMessage() {
    if (_errorMessage == null) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Text(
        _errorMessage!,
        style: GoogleFonts.inter(
          color: Colors.red,
          fontSize: 14,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  // Build submit button
  Widget _buildSubmitButton() {
    return PrimaryButton(
      text: 'Done',
      onPressed: _getOnPressedHandler(),
      width: MediaQuery.of(context).size.width,
      backgroundColor: _primaryColor,
      isLoading: _isLoading,
    );
  }
}
