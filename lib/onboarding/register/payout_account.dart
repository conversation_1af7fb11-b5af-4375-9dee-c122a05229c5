import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mialamobile/components/input_type.dart';
import 'package:mialamobile/components/primary_button.dart';
import 'package:mialamobile/dashboard/dashboard_page.dart';

class PayoutAccount extends StatefulWidget {
  const PayoutAccount({super.key});

  @override
  State<PayoutAccount> createState() => _PayoutAccountState();
}

class _PayoutAccountState extends State<PayoutAccount> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        iconTheme: const IconThemeData(color: Color(0xff8C8C8C)),
      ),
      backgroundColor: const Color(0xff121212),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 32),
            child: Column(mainAxisSize: MainAxisSize.min, children: [
              Text(
                'Payout Account',
                style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w500,
                    fontSize: 24,
                    color: Colors.white),
              ),
              const Gap(10),
              Text(
                'Enter your payout account details',
                style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w400,
                    fontSize: 12,
                    color: const Color(0xffC8C8C8)),
              ),
              const Gap(65),
              InputType(
                labelText: 'Account Number',
                hintText: '**********',
                keyboardType: TextInputType.number,
                controller: TextEditingController(),
              ),
              const Gap(10),
              InputType(
                labelText: 'Bank',
                hintText: 'Bank Name',
                controller: TextEditingController(),
              ),
              const Gap(24),
              PrimaryButton(
                text: 'Done',
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        backgroundColor: const Color(0xff121212),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        content: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Image.asset('assets/images/success.png'),
                            const Gap(10),
                            Text(
                              'Success!',
                              style: GoogleFonts.poppins(
                                fontWeight: FontWeight.w600,
                                fontSize: 24,
                                color: const Color(0xffB10303),
                              ),
                            ),
                            const Gap(4),
                            Text(
                              'You have successfully created an account',
                              textAlign: TextAlign.center,
                              style: GoogleFonts.poppins(
                                fontWeight: FontWeight.w400,
                                fontSize: 12,
                                color: const Color(0xffC8C8C8),
                              ),
                            ),
                            const Gap(24),
                            PrimaryButton(
                              text: 'OK',
                              onPressed: () {
                                Navigator.of(context).pop(); // Close the dialog
                                Navigator.pushReplacement(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          const DashboardPage()),
                                );
                              },
                              width: double.infinity,
                              backgroundColor: const Color(0xffB10303),
                            ),
                          ],
                        ),
                      );
                    },
                  );
                },
                width: MediaQuery.of(context).size.width,
                backgroundColor: const Color(0xffFF0000),
              ),
            ]),
          ),
        ),
      ),
    );
  }
}
