import 'dart:async';

import 'package:flutter/material.dart';
import 'package:mialamobile/onboarding/home_page.dart';
import 'package:mialamobile/providers/auth_provider.dart';
import 'package:mialamobile/screens/home_screen.dart';
import 'package:provider/provider.dart';
import 'dart:developer' as developer;

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _checkAuthAndNavigate();
  }

  Future<void> _checkAuthAndNavigate() async {
    developer.log('Checking authentication status in SplashScreen',
        name: 'splash_screen');

    // Show splash screen for at least 2 seconds for branding purposes
    await Future.delayed(const Duration(seconds: 2));

    if (!mounted) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final isAuthenticated = await authProvider.checkAuthentication();

    developer.log('Authentication check result: $isAuthenticated',
        name: 'splash_screen');

    if (!mounted) return;

    if (isAuthenticated) {
      developer.log('User is authenticated, navigating to HomeScreen',
          name: 'splash_screen');
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => const HomeScreen()),
      );
    } else {
      developer.log('User is not authenticated, navigating to HomePage',
          name: 'splash_screen');
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => const HomePage()),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xff121212),
      body: Center(
        child: Image.asset('assets/images/miala_logo.png'),
      ),
    );
  }
}
