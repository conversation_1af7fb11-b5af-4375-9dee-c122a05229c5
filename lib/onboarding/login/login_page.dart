import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mialamobile/components/input_type.dart';
import 'package:mialamobile/components/primary_button.dart';
import 'package:mialamobile/dashboard/dashboard_page.dart';
import 'package:mialamobile/onboarding/login/forgotten_password.dart';
import 'dart:developer' as developer;
import 'package:provider/provider.dart';
import 'package:mialamobile/providers/auth_provider.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  // Form key for validation
  final _formKey = GlobalKey<FormState>();

  // Controllers for form fields
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  // Focus nodes for tracking field focus
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();

  bool _obscurePassword = true;
  bool _isLoading = false;
  bool _isFormValid = false;

  // Field validation states
  final Map<String, String> _fieldErrors = {
    'email': '',
    'password': '',
  };

  // Track which fields have been touched/focused
  final Map<String, bool> _fieldTouched = {
    'email': false,
    'password': false,
  };

  @override
  void initState() {
    super.initState();
    // Add listeners to controllers to validate on change
    _emailController.addListener(() => _validateField('email'));
    _passwordController.addListener(() => _validateField('password'));

    // Add focus listeners to track when fields are touched
    _emailFocusNode.addListener(() {
      if (_emailFocusNode.hasFocus) {
        setState(() {
          _fieldTouched['email'] = true;
        });
      }
    });

    _passwordFocusNode.addListener(() {
      if (_passwordFocusNode.hasFocus) {
        setState(() {
          _fieldTouched['password'] = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  // Validate a specific field
  void _validateField(String fieldName) {
    String errorMessage = '';

    switch (fieldName) {
      case 'email':
        errorMessage = _validateEmail();
        break;
      case 'password':
        errorMessage = _validatePassword();
        break;
    }

    setState(() {
      _fieldErrors[fieldName] = errorMessage;
      _updateFormValidity();
    });
  }

  // Validate the entire form
  void _validateForm() {
    // Mark all fields as touched
    setState(() {
      _fieldTouched.forEach((key, value) {
        _fieldTouched[key] = true;
      });
    });

    // Validate all fields
    _validateField('email');
    _validateField('password');
  }

  // Update form validity state
  void _updateFormValidity() {
    _isFormValid = _fieldErrors['email']!.isEmpty &&
        _fieldErrors['password']!.isEmpty &&
        _emailController.text.isNotEmpty &&
        _passwordController.text.isNotEmpty;
  }

  // Validate email field
  String _validateEmail() {
    final email = _emailController.text.trim();
    if (email.isEmpty) {
      return 'Email is required';
    }

    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(email)) {
      return 'Please enter a valid email address';
    }

    return '';
  }

  // Validate password field
  String _validatePassword() {
    final password = _passwordController.text;
    if (password.isEmpty) {
      return 'Password is required';
    }

    return '';
  }

  // Handle login process
  Future<void> _handleLogin(BuildContext context) async {
    developer.log('Login button pressed', name: 'login_page');

    // Validate form before proceeding
    _validateForm();
    if (!_isFormValid) {
      developer.log('Validation failed: Form is invalid', name: 'login_page');
      return;
    }

    final email = _emailController.text.trim();
    final password = _passwordController.text.trim();

    // Get auth provider with listen: false to avoid rebuild
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await authProvider.login(email, password);

      // Check if widget is still mounted before updating state
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      if (success) {
        developer.log('Login successful, navigating to home screen',
            name: 'login_page');
        // Store the navigation action to be executed after the async gap
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => const DashboardPage()),
            );
          }
        });
      } else {
        developer.log('Login failed: ${authProvider.error}',
            name: 'login_page');
        // Show snackbar after the frame is built
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(authProvider.error ?? 'Login failed'),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        });
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      // Show error snackbar after the frame is built
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('An unexpected error occurred: $e'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        iconTheme: const IconThemeData(color: Color(0xff8C8C8C)),
      ),
      backgroundColor: const Color(0xff121212),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 32),
            child: Form(
              key: _formKey,
              child: Column(mainAxisSize: MainAxisSize.min, children: [
                Text(
                  'Welcome Back',
                  style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w500,
                      fontSize: 24,
                      color: Colors.white),
                ),
                const Gap(10),
                Text(
                  'Login to your account',
                  style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w400,
                      fontSize: 12,
                      color: const Color(0xffC8C8C8)),
                ),
                const Gap(65),
                InputType(
                  labelText: 'Email',
                  hintText: '<EMAIL>',
                  keyboardType: TextInputType.emailAddress,
                  controller: _emailController,
                  // Only show error if field has been touched
                  errorText:
                      _fieldTouched['email']! ? _fieldErrors['email'] : '',
                  onChanged: (value) {
                    // Mark as touched and validation will be triggered by the controller listener
                    if (!_fieldTouched['email']!) {
                      setState(() {
                        _fieldTouched['email'] = true;
                      });
                    }
                  },
                  // Pass the focus node to track field interaction
                  focusNode: _emailFocusNode,
                ),
                const Gap(10),
                InputType(
                  labelText: 'Enter Password',
                  hintText: '********',
                  obscureText: _obscurePassword,
                  controller: _passwordController,
                  // Only show error if field has been touched
                  errorText: _fieldTouched['password']!
                      ? _fieldErrors['password']
                      : '',
                  onChanged: (value) {
                    // Mark as touched and validation will be triggered by the controller listener
                    if (!_fieldTouched['password']!) {
                      setState(() {
                        _fieldTouched['password'] = true;
                      });
                    }
                  },
                  // Pass the focus node to track field interaction
                  focusNode: _passwordFocusNode,
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscurePassword
                          ? Icons.visibility_off
                          : Icons.visibility,
                      size: 12,
                      color: const Color(0xff8C8C8C),
                    ),
                    onPressed: () {
                      setState(() {
                        _obscurePassword = !_obscurePassword;
                      });
                    },
                  ),
                ),
                const Gap(10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    InkWell(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ForgottenPasswordPage(),
                          ),
                        );
                      },
                      child: Text(
                        'Forgot Password?',
                        style: GoogleFonts.poppins(
                            fontWeight: FontWeight.w400,
                            fontSize: 10,
                            color: const Color(0xffFF0000)),
                      ),
                    ),
                  ],
                ),
                const Gap(24),
                _isLoading
                    ? const CircularProgressIndicator(
                        color: Colors.red,
                      )
                    : PrimaryButton(
                        text: 'Login',
                        onPressed: _isFormValid
                            ? () {
                                _handleLogin(context);
                              }
                            : null,
                        width: MediaQuery.of(context).size.width,
                        backgroundColor: const Color(0xffFF0000),
                        disabledBackgroundColor: const Color(0xFFFF9999),
                      ),
              ]),
            ),
          ),
        ),
      ),
    );
  }
}
