import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mialamobile/components/input_type.dart';
import 'package:mialamobile/components/primary_button.dart';
import 'package:mialamobile/onboarding/login/otp_verification.dart';
import 'package:mialamobile/onboarding/login/verification_type.dart';
import 'package:mialamobile/onboarding/register/register.dart';
import 'package:mialamobile/providers/auth_provider.dart';
import 'package:provider/provider.dart';

class ForgottenPasswordPage extends StatefulWidget {
  const ForgottenPasswordPage({super.key});

  @override
  State<ForgottenPasswordPage> createState() => _ForgottenPasswordPageState();
}

class _ForgottenPasswordPageState extends State<ForgottenPasswordPage> {
  // Controllers for the text fields
  final TextEditingController _emailController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        iconTheme: const IconThemeData(color: Color(0xff8C8C8C)),
      ),
      backgroundColor: const Color(0xff121212),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 32),
            child: Column(mainAxisSize: MainAxisSize.min, children: [
              Text(
                _getPageTitle(),
                style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w500,
                    fontSize: 24,
                    color: Colors.white),
              ),
              const Gap(10),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Don\'t have an account?',
                    style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w300,
                        fontSize: 12,
                        color: const Color(0xffC8C8C8)),
                  ),
                  const Gap(4),
                  InkWell(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const RegistrationPage(),
                        ),
                      );
                    },
                    child: Text(
                      'Sign Up',
                      style: GoogleFonts.poppins(
                          fontWeight: FontWeight.w400,
                          fontSize: 12,
                          color: const Color(0xffB10303)),
                    ),
                  ),
                ],
              ),
              const Gap(65),

              // Show different content based on the current stage
              _buildCurrentStageWidget(),
            ]),
          ),
        ),
      ),
    );
  }

  // Get the title based on current stage
  String _getPageTitle() {
    return 'Forgot Password';
  }

  // Build the appropriate widget based on current stage
  Widget _buildCurrentStageWidget() {
    return _buildEmailVerificationForm();
  }

  // First stage: Email verification form
  Widget _buildEmailVerificationForm() {
    final authProvider = Provider.of<AuthProvider>(context, listen: true);

    return Column(
      children: [
        InputType(
          labelText: 'Email',
          hintText: '<EMAIL>',
          keyboardType: TextInputType.emailAddress,
          controller: _emailController,
        ),
        const Gap(24),
        PrimaryButton(
          text: authProvider.isLoading ? 'Sending...' : 'Verify Email',
          onPressed: authProvider.isLoading
              ? null
              : () async {
                  // Validate email
                  if (_emailController.text.isEmpty) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Please enter your email')),
                    );
                    return;
                  }

                  // Basic email validation
                  final emailRegex =
                      RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
                  if (!emailRegex.hasMatch(_emailController.text)) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('Please enter a valid email address')),
                    );
                    return;
                  }

                  // Call the API to request password reset
                  final result = await authProvider.forgotPassword(
                    email: _emailController.text,
                  );

                  // Handle the response
                  if (!mounted) return;

                  if (result['success']) {
                    // Show success message
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                          content: Text(result['message'] ??
                              'Reset instructions sent to your email')),
                    );

                    // Navigate to OTP verification screen
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => OtpVerification(
                          email: _emailController.text,
                          verificationType: VerificationType.forgotPassword,
                        ),
                      ),
                    );
                  } else {
                    // Show error message
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                          content: Text(result['message'] ??
                              'Failed to send reset instructions')),
                    );
                  }
                },
          width: MediaQuery.of(context).size.width,
          backgroundColor: const Color(0xffFF0000),
        ),
      ],
    );
  }
}
