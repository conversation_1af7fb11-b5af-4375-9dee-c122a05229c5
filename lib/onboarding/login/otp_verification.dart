import 'dart:async';
import 'dart:developer' as developer;

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mialamobile/api/api_service.dart';
import 'package:mialamobile/api/endpoints.dart';
import 'package:mialamobile/components/primary_button.dart';
import 'package:mialamobile/components/registration_success_dialog.dart';
import 'package:mialamobile/dashboard/profile/components/reset_password.dart';
import 'package:mialamobile/onboarding/login/verification_type.dart';
import 'package:mialamobile/providers/auth_provider.dart';
import 'package:pinput/pinput.dart';
import 'package:provider/provider.dart';

class OtpVerification extends StatefulWidget {
  final String? email;
  final VerificationType verificationType;

  const OtpVerification({
    super.key,
    this.email,
    this.verificationType = VerificationType.accountCreation,
  });

  @override
  State<OtpVerification> createState() => _OtpVerificationState();
}

class _OtpVerificationState extends State<OtpVerification> {
  final TextEditingController _pinController = TextEditingController();
  final ApiService _apiService = ApiService();

  String? _errorMessage;
  bool _isLoading = false;

  // Timer for resend functionality
  Timer? _resendTimer;
  int _remainingTime = 30; // 30 seconds countdown
  bool _canResend = false;

  @override
  void initState() {
    super.initState();
    _startResendTimer();
  }

  @override
  void dispose() {
    _pinController.dispose();
    _resendTimer?.cancel();
    super.dispose();
  }

  // Start the countdown timer for resend functionality
  void _startResendTimer() {
    setState(() {
      _remainingTime = 30;
      _canResend = false;
    });

    _resendTimer?.cancel();
    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingTime > 0) {
          _remainingTime--;
        } else {
          _canResend = true;
          timer.cancel();
        }
      });
    });
  }

  // Resend OTP code
  Future<void> _resendOtp() async {
    if (!_canResend) return;

    // Reset the timer and UI state
    _startResendTimer();

    // Show initial feedback to user
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Resending code...'),
          duration: Duration(seconds: 2),
        ),
      );
    }

    try {
      if (widget.email == null || widget.email!.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Email is missing. Please go back and try again.'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 3),
            ),
          );
        }
        return;
      }

      // Determine which API endpoint to use for resending OTP based on verification type
      // For both account creation and forgot password, we'll use the forgot password endpoint
      // This is a placeholder - you would need to implement the actual resend endpoint
      const apiEndpoint = ApiEndpoints.forgotPassword;

      developer.log('Using API endpoint for resend: $apiEndpoint',
          name: 'otp_verification');

      // Here you would typically call the API to resend the OTP
      // For example:
      // final response = await _apiService.post(
      //   apiEndpoint,
      //   body: {
      //     'email': widget.email,
      //   },
      //   requiresAuth: false,
      // );

      // For now, we'll just show a success message
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Code resent successfully'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      developer.log('Error resending OTP: $e', name: 'otp_verification');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to resend code: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // Get the complete OTP code
  String _getOtpCode() {
    return _pinController.text;
  }

  // Verify OTP
  Future<void> _verifyOtp() async {
    final otpCode = _getOtpCode();

    // Validate OTP code
    if (otpCode.length != 6) {
      setState(() {
        _errorMessage = 'Please enter the complete 6-digit code';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      developer.log('Verifying OTP', name: 'otp_verification');
      developer.log(
          'Email: ${widget.email}, OTP: $otpCode, VerificationType: ${widget.verificationType}',
          name: 'otp_verification');

      if (widget.email == null || widget.email!.isEmpty) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Email is missing. Please go back and try again.';
        });
        return;
      }

      // Get the auth provider
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      Map<String, dynamic> result;

      if (widget.verificationType == VerificationType.accountCreation) {
        // For account creation, use the existing API service approach
        developer.log('Using continue-signup endpoint for account creation',
            name: 'otp_verification');

        // Call the continue-signup API
        final response = await _apiService.post(
          ApiEndpoints.otp,
          body: {
            'email': widget.email,
            'otp': otpCode,
          },
          requiresAuth: false,
        );

        developer.log('OTP verification response received',
            name: 'otp_verification');
        developer.log('Response success: ${response.success}',
            name: 'otp_verification');
        developer.log('Response data: ${response.data}',
            name: 'otp_verification');
        developer.log('Response error: ${response.error}',
            name: 'otp_verification');

        setState(() {
          _isLoading = false;
        });

        if (response.success && response.data != null) {
          result = {
            'success': true,
            'message': 'Verification successful',
            'data': response.data['data'],
          };
        } else {
          result = {
            'success': false,
            'message':
                response.error ?? 'Verification failed. Please try again.',
          };
        }
      } else {
        // For forgot password, use the auth provider
        developer.log('Using verify-otp-for-forget-password endpoint',
            name: 'otp_verification');

        // Call the verify OTP for forgot password API
        result = await authProvider.verifyOtpForForgotPassword(
          email: widget.email!,
          otp: otpCode,
        );

        setState(() {
          _isLoading = false;
        });
      }

      // Process the result
      if (result['success']) {
        developer.log('OTP verification successful', name: 'otp_verification');

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message'] ?? 'Verification successful'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );

          // Navigate based on verification type
          if (widget.verificationType == VerificationType.accountCreation) {
            // For account creation, show registration success dialog
            developer.log(
                'Showing registration success dialog for account creation',
                name: 'otp_verification');
            showRegistrationSuccessDialog(context);
          } else {
            // For forgot password, navigate to the reset password screen
            developer.log('Navigating to reset password screen',
                name: 'otp_verification');

            // For the forgot password flow, we'll use the OTP itself as the reset token
            // since the API doesn't return a token in the response
            String resetToken = otpCode;

            developer.log('Using OTP as reset token: $resetToken',
                name: 'otp_verification');

            // Navigate to the reset password screen
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder: (context) => ResetPassword(
                  resetToken: resetToken,
                  email: widget.email,
                ),
              ),
            );
          }
        }
      } else {
        // Extract error message from result
        String errorMessage =
            result['message'] ?? 'Verification failed. Please try again.';

        developer.log('OTP verification failed: $errorMessage',
            name: 'otp_verification');

        setState(() {
          _errorMessage = errorMessage;
        });

        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      developer.log('Error during OTP verification: $e',
          name: 'otp_verification');

      setState(() {
        _isLoading = false;
        _errorMessage = 'An unexpected error occurred. Please try again.';
      });

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('An unexpected error occurred: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Define Pinput default settings
    final defaultPinTheme = PinTheme(
      width: 50,
      height: 50,
      textStyle: const TextStyle(
        fontSize: 30,
        color: Color(0xffB10303),
        fontWeight: FontWeight.w600,
      ),
      decoration: BoxDecoration(
        color: const Color(0xff1E1E1E),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.transparent),
      ),
    );

    final focusedPinTheme = defaultPinTheme.copyDecorationWith(
      border: Border.all(color: const Color(0xffB10303)),
    );

    final submittedPinTheme = defaultPinTheme.copyWith(
      decoration: defaultPinTheme.decoration?.copyWith(
        color: const Color(0xff1E1E1E),
      ),
    );

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        iconTheme: const IconThemeData(color: Color(0xff8C8C8C)),
      ),
      backgroundColor: const Color(0xff121212),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 32),
            child: Column(mainAxisSize: MainAxisSize.min, children: [
              Text(
                'OTP Verification',
                style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w500,
                    fontSize: 24,
                    color: Colors.white),
              ),
              const Gap(10),
              Text(
                widget.verificationType == VerificationType.accountCreation
                    ? 'We sent a code to verify your account'
                    : 'We sent a code to reset your password',
                style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w400,
                    fontSize: 12,
                    color: const Color(0xffC8C8C8)),
              ),
              const Gap(24),
              // Pinput OTP input
              Pinput(
                length: 6,
                controller: _pinController,
                defaultPinTheme: defaultPinTheme,
                focusedPinTheme: focusedPinTheme,
                submittedPinTheme: submittedPinTheme,
                pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
                showCursor: true,
                onCompleted: (pin) {
                  // Auto-submit when all digits are entered
                  if (pin.length == 6 && !_isLoading) {
                    _verifyOtp();
                  }
                },
              ),

              // Error message
              if (_errorMessage != null) ...[
                const Gap(10),
                Text(
                  _errorMessage!,
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w400,
                    fontSize: 12,
                    color: Colors.red,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],

              const Gap(10),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Didn\'t receive code?',
                    style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w300,
                        fontSize: 10,
                        color: const Color(0xff8C8C8C)),
                  ),
                  const Gap(4),
                  InkWell(
                    onTap: _canResend ? _resendOtp : null,
                    child: Text(
                      _canResend ? 'Resend' : 'Resend ${_remainingTime}s',
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w400,
                        fontSize: 10,
                        color: _canResend
                            ? const Color(0xffFF0000)
                            : const Color(0xff8C8C8C),
                      ),
                    ),
                  ),
                ],
              ),
              const Gap(24),
              PrimaryButton(
                text: 'Done',
                onPressed: _isLoading ? null : _verifyOtp,
                width: MediaQuery.of(context).size.width,
                backgroundColor: const Color(0xffFF0000),
                disabledBackgroundColor: const Color(0xFFFF9999),
                isLoading: _isLoading,
              ),
            ]),
          ),
        ),
      ),
    );
  }
}
