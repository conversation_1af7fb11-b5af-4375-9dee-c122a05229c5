import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class InputDropdown extends StatelessWidget {
  final String labelText;
  final String hintText;
  final List<DropdownMenuItem<String>> items;
  final String? value;
  final Function(String?)? onChanged;
  final String? Function(String?)? validator;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final Color borderColor;
  final Color focusedBorderColor;
  final Color labelColor;
  final Color hintColor;
  final Color textColor;
  final Color fillColor;
  final bool filled;
  final double borderRadius;
  final EdgeInsetsGeometry contentPadding;
  final String? errorText;
  final bool isLoading;

  const InputDropdown({
    super.key,
    required this.labelText,
    required this.hintText,
    required this.items,
    this.value,
    this.onChanged,
    this.validator,
    this.suffixIcon,
    this.prefixIcon,
    this.borderColor = Colors.transparent,
    this.focusedBorderColor = const Color(0xffB10303),
    this.labelColor = const Color(0xFFFFFFFF),
    this.hintColor = const Color(0xFF8C8C8C),
    this.textColor = Colors.white,
    this.fillColor = const Color(0xff1E1E1E),
    this.filled = true,
    this.borderRadius = 4.0,
    this.contentPadding =
        const EdgeInsets.symmetric(vertical: 11.0, horizontal: 11.0),
    this.errorText,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          labelText,
          style: GoogleFonts.poppins(
            color: labelColor,
            fontSize: 10.0,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8.0),
        Container(
          // Set exact height to 47 pixels as specified
          height: 47,
          width: double.infinity,
          decoration: BoxDecoration(
            color: fillColor,
            borderRadius: BorderRadius.circular(borderRadius),
            border: Border.all(
              // Only show red border if there's an actual error message
              color: errorText != null && errorText!.isNotEmpty
                  ? Colors.red
                  : borderColor,
              width: errorText != null && errorText!.isNotEmpty ? 1.0 : 0.5,
            ),
          ),
          child: Stack(
            children: [
              // Add a placeholder text that's always visible when no value is selected
              if (value == null && !isLoading)
                Positioned(
                  left: 15, // Increased left padding to 15px
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: Text(
                      hintText,
                      style: GoogleFonts.poppins(
                        color: hintColor,
                        fontSize: 14.0,
                        fontWeight: FontWeight.w400,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              // Show loading indicator when isLoading is true
              if (isLoading)
                Positioned(
                  right: 15, // Adjusted right padding to 15px
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: SizedBox(
                      height: 16,
                      width: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: hintColor,
                      ),
                    ),
                  ),
                ),
              // Position the dropdown over the placeholder
              AbsorbPointer(
                absorbing: isLoading, // Disable interaction when loading
                child: DropdownButtonFormField<String>(
                  value: value,
                  onChanged: onChanged,
                  validator: validator,
                  icon: isLoading
                      ? const SizedBox
                          .shrink() // Hide dropdown icon when loading
                      : (suffixIcon ??
                          const Padding(
                            padding: EdgeInsets.only(right: 8.0),
                            child: Icon(
                              Icons
                                  .arrow_drop_down, // Simple downward-pointing triangle icon
                              color: Color(0XFF8E8E8E),
                              size: 24,
                            ),
                          )),
                  iconSize: 24,
                  // Add space between items to match design
                  itemHeight: 48,
                  // Style for the selected value - ensure it's white
                  style: GoogleFonts.poppins(
                    color: textColor, // This is white by default
                    fontSize: 14.0,
                    fontWeight: FontWeight.w400,
                  ),
                  // Add hint to ensure it's displayed when no value is selected
                  hint: Text(
                    isLoading ? 'Loading...' : hintText,
                    style: GoogleFonts.poppins(
                      color: hintColor,
                      fontSize: 14.0,
                      fontWeight: FontWeight.w400,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  // Ensure selected item text color is white
                  selectedItemBuilder: (BuildContext context) {
                    return items.map<Widget>((DropdownMenuItem<String> item) {
                      return Container(
                        alignment: AlignmentDirectional.centerStart,
                        child: Text(
                          // Find the item's child text
                          item.child is Text
                              ? (item.child as Text).data ?? ''
                              : '',
                          style: GoogleFonts.poppins(
                            color:
                                textColor, // Force white color for selected item
                            fontSize: 14.0,
                            fontWeight: FontWeight.w400,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      );
                    }).toList();
                  },
                  dropdownColor: fillColor,
                  items: isLoading ? [] : items, // Empty items when loading
                  // Add isExpanded to true to prevent overflow
                  isExpanded: true,
                  // Add menuMaxHeight to limit dropdown height and enable scrolling
                  menuMaxHeight: 300,
                  // Add alignment to ensure proper positioning
                  alignment: AlignmentDirectional.centerStart,
                  decoration: InputDecoration(
                    // Keep hintText in decoration for backward compatibility
                    hintText: isLoading ? 'Loading...' : hintText,
                    hintStyle: GoogleFonts.poppins(
                      color: hintColor,
                      fontSize: 14.0,
                      fontWeight: FontWeight.w400,
                    ),
                    alignLabelWithHint: true,
                    prefixIcon: prefixIcon,
                    // Set padding to 5px vertical and 15px horizontal as specified
                    contentPadding: const EdgeInsets.symmetric(
                        vertical: 5.0, horizontal: 15.0),
                    filled:
                        false, // Set to false since we're using a Container with background
                    // Ensure the border doesn't contribute to overflow
                    isDense: true,
                    // Use transparent borders since we're using the Container for borders
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(borderRadius),
                      borderSide: const BorderSide(color: Colors.transparent),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(borderRadius),
                      borderSide: const BorderSide(color: Colors.transparent),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(borderRadius),
                      borderSide: BorderSide(color: focusedBorderColor),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(borderRadius),
                      borderSide: const BorderSide(color: Colors.transparent),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(borderRadius),
                      borderSide: const BorderSide(color: Colors.transparent),
                    ),
                    errorStyle: const TextStyle(
                        height: 0,
                        fontSize: 0), // Hide error text in decoration
                  ),
                ),
              ),
            ],
          ),
        ),
        // Display error text below the dropdown
        if (errorText != null && errorText!.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 4.0, left: 4.0),
            child: Text(
              errorText!,
              style: GoogleFonts.poppins(
                color: Colors.red,
                fontSize: 10.0,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
      ],
    );
  }
}
