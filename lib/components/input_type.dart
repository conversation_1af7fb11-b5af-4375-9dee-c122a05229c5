import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class InputType extends StatelessWidget {
  final String labelText;
  final String hintText;
  final TextEditingController? controller;
  final bool obscureText;
  final TextInputType keyboardType;
  final Function(String)? onChanged;
  final String? Function(String?)? validator;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final Color borderColor;
  final Color focusedBorderColor;
  final Color labelColor;
  final Color hintColor;
  final Color textColor;
  final Color fillColor;
  final bool filled;
  final double borderRadius;
  final EdgeInsetsGeometry contentPadding;
  final int? maxLines;
  final int? minLines;
  final String? errorText;
  final bool enabled;
  final FocusNode? focusNode;

  const InputType({
    super.key,
    required this.labelText,
    required this.hintText,
    this.controller,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.onChanged,
    this.validator,
    this.suffixIcon,
    this.prefixIcon,
    this.borderColor = Colors.transparent,
    this.focusedBorderColor = const Color(0xffB10303),
    this.labelColor = const Color(0xFFFFFFFF),
    this.hintColor = const Color(0xFF8C8C8C),
    this.textColor = Colors.white,
    this.fillColor = const Color(0xff1E1E1E),
    this.filled = true,
    this.borderRadius = 4.0,
    this.contentPadding =
        const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
    this.maxLines = 1,
    this.minLines,
    this.errorText,
    this.enabled = true,
    this.focusNode,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          labelText,
          style: GoogleFonts.poppins(
            color: labelColor,
            fontSize: 10.0,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8.0),
        SizedBox(
          height: errorText != null && errorText!.isNotEmpty
              ? 42 + 20
              : 42, // Add height for error text
          width: MediaQuery.of(context).size.width,
          child: TextFormField(
            controller: controller,
            obscureText: obscureText,
            keyboardType: keyboardType,
            onChanged: onChanged,
            validator: validator,
            maxLines: maxLines,
            minLines: minLines,
            enabled: enabled,
            focusNode: focusNode,
            style: GoogleFonts.poppins(
              color: enabled ? textColor : Colors.grey,
              fontSize: 14.0,
              fontWeight: FontWeight.w400,
            ),
            decoration: InputDecoration(
              hintText: hintText,
              hintStyle: GoogleFonts.poppins(
                color: hintColor,
                fontSize: 10.0,
                fontWeight: FontWeight.w300,
              ),
              suffixIcon: suffixIcon,
              prefixIcon: prefixIcon,
              contentPadding: contentPadding,
              filled: filled,
              fillColor: fillColor,
              errorText:
                  errorText != null && errorText!.isNotEmpty ? errorText : null,
              errorStyle: GoogleFonts.poppins(
                color: Colors.red,
                fontSize: 10.0,
                fontWeight: FontWeight.w400,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(borderRadius),
                borderSide: BorderSide(color: borderColor),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(borderRadius),
                borderSide: BorderSide(color: borderColor),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(borderRadius),
                borderSide: BorderSide(color: focusedBorderColor),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(borderRadius),
                borderSide: const BorderSide(color: Colors.red),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(borderRadius),
                borderSide: const BorderSide(color: Colors.red),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
