import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';

class NotificationBadge extends StatelessWidget {
  final String iconPath;
  final int badgeCount;
  final VoidCallback? onTap;
  final double iconSize;
  final Color? iconColor;

  const NotificationBadge({
    super.key,
    required this.iconPath,
    required this.badgeCount,
    this.onTap,
    this.iconSize = 24.0,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Main notification icon
          SvgPicture.asset(
            iconPath,
            width: iconSize,
            height: iconSize,
            colorFilter: iconColor != null
                ? ColorFilter.mode(iconColor!, BlendMode.srcIn)
                : null,
          ),

          // Badge indicator
          if (badgeCount > 0)
            Positioned(
              top: -6,
              right: -6,
              child: Container(
                constraints: const BoxConstraints(
                  minWidth: 16,
                  minHeight: 16,
                ),
                padding: const EdgeInsets.symmetric(horizontal: 4),
                decoration: const BoxDecoration(
                  color: Color(0xffB10303), // App's red theme color
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    _getBadgeText(),
                    style: GoogleFonts.poppins(
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                      height: 1.0,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Format badge text to handle large numbers
  String _getBadgeText() {
    if (badgeCount > 99) {
      return '99+';
    } else if (badgeCount > 9) {
      return '9+';
    } else {
      return badgeCount.toString();
    }
  }
}
