import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mialamobile/components/primary_button.dart';
import 'package:mialamobile/providers/auth_provider.dart';
import 'package:provider/provider.dart';
import 'dart:developer' as developer;

class RegistrationSuccessDialog extends StatefulWidget {
  const RegistrationSuccessDialog({super.key});

  @override
  State<RegistrationSuccessDialog> createState() =>
      _RegistrationSuccessDialogState();
}

class _RegistrationSuccessDialogState extends State<RegistrationSuccessDialog> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: const Color(0xff1E1E1E),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Success icon
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: const Color(0xff4CAF50),
                borderRadius: BorderRadius.circular(30),
              ),
              child: const Icon(
                Icons.check,
                color: Colors.white,
                size: 36,
              ),
            ),
            const Gap(20),
            // Title
            Text(
              'Registration Successful!',
              style: GoogleFonts.poppins(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
            const Gap(12),
            // Message
            Text(
              'Your account has been created successfully. Please wait while an admin reviews and approves your registration. You will be notified once your account is approved.',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: const Color(0xffC8C8C8),
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const Gap(28),
            // OK Button
            _isLoading
                ? const CircularProgressIndicator(
                    color: Color(0xffB10303),
                  )
                : PrimaryButton(
                    text: 'Got it',
                    onPressed: () => _handleOkPressed(context),
                    width: double.infinity,
                    backgroundColor: const Color(0xffB10303),
                    borderRadius: 8,
                    fontWeight: FontWeight.w600,
                    textSize: 16,
                  ),
          ],
        ),
      ),
    );
  }

  // Handle OK button press
  Future<void> _handleOkPressed(BuildContext context) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Clear authentication state since user needs admin approval
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      await authProvider.clearAuthenticationForPendingApproval();

      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      // Close the dialog
      if (context.mounted) {
        Navigator.of(context).pop();

        // Navigate back to login screen and clear navigation stack
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/onboarding',
          (route) => false, // This prevents going back
        );
      }

      developer.log(
          'Navigated to login screen after registration approval dialog',
          name: 'registration_success_dialog');
    } catch (e) {
      developer.log('Error handling registration success dialog: $e',
          name: 'registration_success_dialog');

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // Still close dialog and navigate even if there's an error
        if (context.mounted) {
          Navigator.of(context).pop();
          Navigator.of(context).pushNamedAndRemoveUntil(
            '/onboarding',
            (route) => false,
          );
        }
      }
    }
  }
}

// Helper function to show the dialog
void showRegistrationSuccessDialog(BuildContext context) {
  showDialog(
    context: context,
    barrierDismissible: false, // Prevent dismissing by tapping outside
    barrierColor: const Color(
        0xB3000000), // Darker barrier for better contrast (70% opacity)
    useSafeArea: true, // Ensure dialog respects safe area
    builder: (BuildContext context) => const RegistrationSuccessDialog(),
  );
}
