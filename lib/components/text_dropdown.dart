import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class TextDropdown extends StatelessWidget {
  final String value;
  final List<String> items;
  final ValueChanged<String?> onChanged;
  final IconData icon;
  final double iconSize;
  final Color iconColor;
  final Color? dropdownColor;
  final double? dropdownWidth;
  final Color? selectedItemColor;
  final Color? itemColor;

  const TextDropdown({
    super.key,
    required this.value,
    required this.items,
    required this.onChanged,
    this.icon = Icons.arrow_drop_down_outlined,
    this.iconSize = 20,
    this.iconColor = const Color(0xffB10303),
    this.dropdownColor,
    this.dropdownWidth,
    this.selectedItemColor,
    this.itemColor,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButton<String>(
      value: value,
      icon: Icon(icon, size: iconSize, color: iconColor),
      iconSize: iconSize,
      elevation: 16,
      style: GoogleFonts.poppins(
        fontWeight: FontWeight.w500,
        color: const Color(0xffB10303),
        fontSize: 16,
      ),
      dropdownColor:
          dropdownColor ?? const Color(0xff1E1E1E), // Dark gray from memory
      menuMaxHeight: 200,
      isExpanded: dropdownWidth != null,
      itemHeight: 48,
      underline: Container(),
      onChanged: onChanged,
      items: items.map<DropdownMenuItem<String>>((String value) {
        return DropdownMenuItem<String>(
          value: value,
          child: Container(
            // width: dropdownWidth,
            padding: const EdgeInsets.symmetric(horizontal: 0),
            child: Text(
              value,
              style: GoogleFonts.poppins(
                color: this.value == value
                    ? (selectedItemColor ?? const Color(0xffB10303))
                    : (itemColor ?? Colors.white),
                fontSize: 14,
                fontWeight:
                    this.value == value ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}
