import 'package:flutter/material.dart';
import 'package:mialamobile/onboarding/home_page.dart';
import 'package:mialamobile/onboarding/login/login_page.dart';
import 'package:mialamobile/onboarding/splashscreen.dart';
import 'package:mialamobile/providers/auth_provider.dart';
import 'package:mialamobile/screens/home_screen.dart';
import 'package:provider/provider.dart';

void main() {
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Mialla Mobile',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: const Color(0xffFF0000)),
        useMaterial3: true,
      ),
      initialRoute: '/',
      routes: {
        '/': (context) => const SplashScreen(),
        '/home': (context) => const HomeScreen(),
        '/login': (context) => const LoginPage(),
        '/onboarding': (context) => const HomePage(),
      },
    );
  }
}
