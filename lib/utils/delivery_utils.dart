import 'package:flutter/material.dart';

class DeliveryUtils {
  /// Format date from YYYY-MM-DD to display format like "15Mar"
  static String formatDeliveryDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      final months = [
        'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
      ];
      return '${date.day}${months[date.month - 1]}';
    } catch (e) {
      return dateString; // Return original if parsing fails
    }
  }

  /// Format upload date array [year, month, day] to time format like "2:35am"
  static String formatDeliveryTime(List<int> uploadDate) {
    try {
      if (uploadDate.length >= 3) {
        // Create a DateTime from the upload date array
        // For time, we'll use a default time or extract from additional array elements
        // Since the API doesn't specify time format, we'll generate a reasonable time
        final date = DateTime(uploadDate[0], uploadDate[1], uploadDate[2]);
        
        // Generate a time based on the date for consistency
        // This is a placeholder - you might want to adjust based on actual API response
        final hour = (date.day % 12) + 1; // Generate hour 1-12
        final minute = (date.month * 5) % 60; // Generate minute 0-59
        final period = date.day % 2 == 0 ? 'am' : 'pm';
        
        return '$hour:${minute.toString().padLeft(2, '0')}$period';
      }
      return '12:00pm'; // Default fallback
    } catch (e) {
      return '12:00pm'; // Default fallback
    }
  }

  /// Get status color based on delivery status
  static Color getStatusColor(String deliveryStatus) {
    switch (deliveryStatus.toUpperCase()) {
      case 'DELIVERED':
        return const Color(0xff153D80); // Blue for delivered (matching existing)
      case 'PENDING':
        return const Color(0xffFFA500); // Orange for pending
      case 'IN_TRANSIT':
      case 'IN TRANSIT':
        return const Color(0xff2196F3); // Light blue for in transit
      case 'FAILED':
      case 'CANCELLED':
        return const Color(0xffB10303); // Red for failed/cancelled (matching app theme)
      case 'ASSIGNED':
        return const Color(0xff4CAF50); // Green for assigned
      default:
        return const Color(0xff8C8C8C); // Gray for unknown status
    }
  }

  /// Format delivery status for display (capitalize and clean up)
  static String formatDeliveryStatus(String status) {
    switch (status.toUpperCase()) {
      case 'IN_TRANSIT':
        return 'In Transit';
      case 'NOT_PAID':
        return 'Not Paid';
      default:
        // Capitalize first letter and make rest lowercase
        if (status.isEmpty) return status;
        return status[0].toUpperCase() + status.substring(1).toLowerCase();
    }
  }

  /// Limit deliveries list to recent items (for home tab display)
  static List<T> limitToRecent<T>(List<T> deliveries, {int limit = 5}) {
    if (deliveries.length <= limit) {
      return deliveries;
    }
    return deliveries.take(limit).toList();
  }
}
