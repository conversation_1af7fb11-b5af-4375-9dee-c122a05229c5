class NotificationModel {
  final int id;
  final int recipientId;
  final String message;
  final bool read;
  final List<int> createdAt;
  final String type;

  NotificationModel({
    required this.id,
    required this.recipientId,
    required this.message,
    required this.read,
    required this.createdAt,
    required this.type,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] ?? 0,
      recipientId: json['recipientId'] ?? 0,
      message: json['message'] ?? '',
      read: json['read'] ?? false,
      createdAt: json['createdAt'] != null
          ? List<int>.from(json['createdAt'])
          : [0, 0, 0, 0, 0, 0, 0],
      type: json['type'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'recipientId': recipientId,
      'message': message,
      'read': read,
      'createdAt': createdAt,
      'type': type,
    };
  }

  /// Convert timestamp array to DateTime
  DateTime get dateTime {
    try {
      if (createdAt.length >= 6) {
        return DateTime(
          createdAt[0], // year
          createdAt[1], // month
          createdAt[2], // day
          createdAt[3], // hour
          createdAt[4], // minute
          createdAt[5], // second
          createdAt.length > 6 ? (createdAt[6] ~/ 1000000) : 0, // milliseconds from nanoseconds
        );
      }
      return DateTime.now();
    } catch (e) {
      return DateTime.now();
    }
  }

  /// Get formatted time string (e.g., "08:00am")
  String get formattedTime {
    final dt = dateTime;
    final hour = dt.hour;
    final minute = dt.minute;
    final period = hour >= 12 ? 'pm' : 'am';
    final displayHour = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    return '${displayHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}$period';
  }

  /// Get formatted date string (e.g., "Today", "Yesterday", "15 Mar")
  String get formattedDate {
    final dt = dateTime;
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final notificationDate = DateTime(dt.year, dt.month, dt.day);

    if (notificationDate == today) {
      return 'Today';
    } else if (notificationDate == yesterday) {
      return 'Yesterday';
    } else {
      final months = [
        'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
      ];
      return '${dt.day} ${months[dt.month - 1]}';
    }
  }

  /// Get icon path based on notification type
  String get iconPath {
    switch (type.toUpperCase()) {
      case 'DELIVERY_ASSIGNED':
        return 'assets/icons/order.svg';
      case 'PAYMENT_RECEIVED':
        return 'assets/icons/star_check.svg';
      case 'DELIVERY_FAILED':
        return 'assets/icons/failed_circle.svg';
      case 'PAYOUT_PROCESSED':
        return 'assets/icons/check_circle.svg';
      case 'REMINDER':
        return 'assets/icons/pending.svg';
      case 'ADMIN_MESSAGE':
        return 'assets/icons/message.svg';
      default:
        return 'assets/icons/notification.svg';
    }
  }

  /// Create a copy with updated read status
  NotificationModel copyWith({
    int? id,
    int? recipientId,
    String? message,
    bool? read,
    List<int>? createdAt,
    String? type,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      recipientId: recipientId ?? this.recipientId,
      message: message ?? this.message,
      read: read ?? this.read,
      createdAt: createdAt ?? this.createdAt,
      type: type ?? this.type,
    );
  }

  @override
  String toString() {
    return 'NotificationModel(id: $id, message: $message, read: $read, type: $type, dateTime: $dateTime)';
  }
}
