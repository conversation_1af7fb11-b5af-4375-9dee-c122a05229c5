class DeliverySummary {
  final int totalDeliveries;
  final int deliveredCount;
  final int pendingCount;

  DeliverySummary({
    required this.totalDeliveries,
    required this.deliveredCount,
    required this.pendingCount,
  });

  factory DeliverySummary.fromJson(Map<String, dynamic> json) {
    return DeliverySummary(
      totalDeliveries: json['totalDeliveries'] ?? 0,
      deliveredCount: json['deliveredCount'] ?? 0,
      pendingCount: json['pendingCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalDeliveries': totalDeliveries,
      'deliveredCount': deliveredCount,
      'pendingCount': pendingCount,
    };
  }

  @override
  String toString() {
    return 'DeliverySummary(totalDeliveries: $totalDeliveries, deliveredCount: $deliveredCount, pendingCount: $pendingCount)';
  }
}
