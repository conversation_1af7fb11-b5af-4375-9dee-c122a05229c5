class DvaInfo {
  final String accountName;
  final String accountNumber;
  final String bankName;
  final double balance;

  DvaInfo({
    required this.accountName,
    required this.accountNumber,
    required this.bankName,
    required this.balance,
  });

  factory DvaInfo.fromJson(Map<String, dynamic> json) {
    return DvaInfo(
      accountName: json['accountName'] ?? '',
      accountNumber: json['accountNumber'] ?? '',
      bankName: json['bankName'] ?? '',
      balance: (json['balance'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'accountName': accountName,
      'accountNumber': accountNumber,
      'bankName': bankName,
      'balance': balance,
    };
  }

  @override
  String toString() {
    return 'DvaInfo(accountName: $accountName, accountNumber: $accountNumber, bankName: $bankName, balance: $balance)';
  }
}
